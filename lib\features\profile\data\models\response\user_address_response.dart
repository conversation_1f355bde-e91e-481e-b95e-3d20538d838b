import 'package:json_annotation/json_annotation.dart';
part 'user_address_response.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
  includeIfNull: false,
)
class UserAddress {
  UserAddress({
    required this.stateName,
    required this.longitude,
    required this.latitude,
    required this.zipCode,
    required this.cityName,
    required this.stateId,
  });
  final String stateName;
  final double longitude;
  final double latitude;
  final String zipCode;
  final String cityName;
  final String stateId;

  factory UserAddress.fromJson(Map<String, dynamic> json) =>
      _$UserAddressFromJson(json);
}
