import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/repositories/buyer_location_filter_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DeviceCurrentLocationCubit extends Cubit<DeviceCurrentLocationState> {
  final BuyerLocationFilterRepository repository;
  DeviceCurrentLocationCubit({
    required this.repository,
  }) : super(const DeviceCurrentLocationLoading());

  Future<LocationEntity?> fetch() async {
    emit(const DeviceCurrentLocationLoading());
    try {
      final location = await repository.getCurrentLocation();
      emit(DeviceCurrentLocationLoaded(location));
      return location;
    } catch (e) { 
      emit(DeviceCurrentLocationError(e.toString()));
      return null;
    }
  }
}

sealed class DeviceCurrentLocationState {
  const DeviceCurrentLocationState();
}

class DeviceCurrentLocationLoading extends DeviceCurrentLocationState {
  const DeviceCurrentLocationLoading();
}

class DeviceCurrentLocationLoaded extends DeviceCurrentLocationState {
  final LocationEntity location;
  const DeviceCurrentLocationLoaded(this.location);
}

class DeviceCurrentLocationError extends DeviceCurrentLocationState {
  final String message;
  const DeviceCurrentLocationError(this.message);
}
