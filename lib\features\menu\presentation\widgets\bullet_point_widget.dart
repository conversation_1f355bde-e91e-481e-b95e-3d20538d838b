import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:flutter/material.dart';

class BulletPointWidget extends StatelessWidget {
  const BulletPointWidget({super.key, required this.text});

  final String text;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Text(
                '•',
                style: context.typography.smallReg.copyWith(
                  color: context.appColors.greyDefaultGreyMedium,
                ),
              ),
            ),
            Expanded(
              child: RichText(
                text: TextSpan(
                  style: context.typography.smallReg.copyWith(
                    color: context.appColors.greyDefaultGreyMedium,
                  ),
                  children: [
                    TextSpan(
                      text: text,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),
      ],
    );
  }
}