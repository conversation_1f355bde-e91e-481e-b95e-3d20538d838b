// import 'package:json_annotation/json_annotation.dart';

// part 'filters_request_model.g.dart';

// @JsonSerializable(
//     fieldRename: FieldRename.snake, includeIfNull: false, explicitToJson: true)
// class FiltersRequestModel {
//   final SortOptions sortOptions;
//   final Filters filters;

//   FiltersRequestModel({
//     this.sortOptions = const SortOptions(),
//     required this.filters,
//   });

//   factory FiltersRequestModel.fromJson(Map<String, dynamic> json) =>
//       _$FiltersRequestModelFromJson(json);

//   Map<String, dynamic> toJson() => _$FiltersRequestModelToJson(this);
// }

// @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
// class SortOptions {
//   final String buyerLocationsOfInterest;
//   final String insertedAt;

//   const SortOptions(
//       {this.buyerLocationsOfInterest = 'asc', this.insertedAt = 'desc'});

//   factory SortOptions.fromJson(Map<String, dynamic> json) =>
//       _$SortOptionsFromJson(json);

//   Map<String, dynamic> toJson() => _$SortOptionsToJson(this);
// }

// @JsonSerializable(fieldRename: FieldRename.snake, includeIfNull: false)
// class Filters {
//   final String? purchaseType;
//   final String? propertyType;
//   final String? financialStatus;
//   final String? timeline;
//   final String? minBedrooms;
//   final String? minBathrooms;
//   final String? minArea;
//   final String? searchZipCode;

//   const Filters({
//     this.purchaseType,
//     this.propertyType,
//     this.financialStatus,
//     this.timeline,
//     this.minBedrooms,
//     this.minBathrooms,
//     this.minArea,
//     this.searchZipCode,
//   });

//   factory Filters.fromJson(Map<String, dynamic> json) =>
//       _$FiltersFromJson(json);

//   Map<String, dynamic> toJson() => _$FiltersToJson(this);
// }
