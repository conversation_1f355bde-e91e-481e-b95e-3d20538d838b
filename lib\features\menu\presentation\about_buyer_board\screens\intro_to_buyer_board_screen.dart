import 'package:buyer_board/common/widgets/app_bar.dart';
import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/menu/cubit/intro_to_buyer_board_cubit.dart';
import 'package:buyer_board/features/menu/presentation/about_buyer_board/state/intro_to_buyer_board_state.dart';
import 'package:buyer_board/features/onboarding/data/onboarding_model.dart';
import 'package:buyer_board/features/onboarding/presentation/widgets/onboarding_bullets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/resources/text_styles.dart';
import '../../../../onboarding/presentation/widgets/onboarding_page_indicators.dart';

class IntroToBuyerBoardScreen extends StatefulWidget {
  const IntroToBuyerBoardScreen({super.key});

  @override
  State<IntroToBuyerBoardScreen> createState() =>
      _IntroToBuyerBoardScreenState();
}

class _IntroToBuyerBoardScreenState extends State<IntroToBuyerBoardScreen> {
  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Scaffold(
      // backgroundColor: AppColors.white,
      appBar: ApplicationAppBar.buildAppBar(context,
          titleWidget: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                Strings.introToBuyerBoard,
                style: context.textTheme.titleLarge?.copyWith(
                  color: colors.onSurface,
                ),
              ),
              Text(
                Strings.aboutBuyerBoard,
                style: context.textTheme.bodySmall?.copyWith(
                  color: colors.inversePrimary,
                ),
              ),
            ],
          ),
          leadingWidget: const SizedBox.shrink(),
          actions: [
            TextButton(
              child: Text(
                Strings.done,
                style: AppStyles.large.copyWith(
                    color: colors.onSurface, fontSize: Dimensions.padding_20),
              ),
              onPressed: () => context.shouldPop(),
            )
          ]),
      body: BlocBuilder<IntroToBuyerBoardCubit, IntroToBuyerBoardState>(
          builder: (context, state) => state.maybeWhen(
                success: (onboardings) => IntroToBuyerBoardWidget(
                  onboardings: onboardings,
                ),
                orElse: () => const SizedBox.shrink(),
              )),
    );
  }
}

class IntroToBuyerBoardWidget extends StatelessWidget {
  IntroToBuyerBoardWidget({
    super.key,
    required this.onboardings,
  });
  final List<OnboardingModel> onboardings;
  final introToBuyerBoardValueNotifier = ValueNotifier<int>(0);

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return ValueListenableBuilder<int>(
        valueListenable: introToBuyerBoardValueNotifier,
        builder: (context, index, _) {
          return Column(
            children: [
              Image.asset(onboardings[index].image),
              const SizedBox(height: 30),
              OnBoardingPageIndicators(
                selectedIndex: index,
                indicatorColor: colors.primary,
              ),
              const SizedBox(height: 16),
              if (onboardings[index].leading != null)
                Text(
                  onboardings[index].leading!,
                  style: AppStyles.smallSemiBold.copyWith(
                    color: colors.primary,
                  ),
                ),
              Text(
                onboardings[index].heading!,
                style: AppStyles.mediumBold.copyWith(
                    color: colors.primary,
                    fontSize: Dimensions.materialPadding_22),
              ),
              const SizedBox(height: Dimensions.padding_24),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.padding_24),
                child: Text(
                  onboardings[index].description!,
                  style: AppStyles.large
                      .copyWith(color: colors.primary, fontSize: 20),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.padding_24),
                child: OnBoardingBullets(
                  bullets: onboardings[index].bullets ?? [],
                  bulletPointColor: colors.primary,
                ),
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.padding_24),
                child: _buildButton(index, context),
              ),
              const SizedBox(height: 64)
            ],
          );
        });
  }

  Widget _buildButton(int index, BuildContext context) {
    return index == 0
        ? _buildNextButton(index, context)
        : _buildPreviousNextButtons(index, context);
  }

  Widget _buildNextButton(int index, BuildContext context) {
    return CommonButton.basic(
      label: Strings.next,
      action: () => introToBuyerBoardValueNotifier.value = index + 1,
      textColor: context.colorScheme.primary,
      backgroundColor: Colors.transparent,
      borderColor: context.colorScheme.primary,
    );
  }

  Widget _buildPreviousNextButtons(int index, BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: CommonButton.basic(
            label: Strings.previous,
            action: () => introToBuyerBoardValueNotifier.value = index - 1,
            textColor: context.colorScheme.primary,
            backgroundColor: Colors.transparent,
            borderColor: context.colorScheme.primary,
          ),
        ),
        const SizedBox(
          width: Dimensions.padding_8,
        ),
        Expanded(
          child: CommonButton.basic(
            label:
                index == onboardings.length - 1 ? Strings.done : Strings.next,
            action: index == onboardings.length - 1
                ? () => context.shouldPop()
                : () => introToBuyerBoardValueNotifier.value = index + 1,
            textColor: context.colorScheme.primary,
            backgroundColor: Colors.transparent,
            borderColor: context.colorScheme.primary,
          ),
        ),
      ],
    );
  }
}
