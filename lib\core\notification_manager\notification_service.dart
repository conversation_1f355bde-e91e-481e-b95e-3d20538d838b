import 'package:buyer_board/core/app/app_config.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

abstract class NotificationService {
  static Future<void> initialize(AppConfig appConfig) async {
    await OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
    OneSignal.initialize(appConfig.oneSignalAppId);
    OneSignal.Notifications.requestPermission(true);
  }

  static Future<void> login(int userId) async {
    await OneSignal.login('$userId');
    await OneSignal.User.addTagWithKey('userId', userId);
  }

  static Future<void> logout() async {
    await OneSignal.logout();
  }
}
