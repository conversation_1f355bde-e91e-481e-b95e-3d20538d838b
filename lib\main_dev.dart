import 'package:buyer_board/core/app/app.dart';
import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/notification_manager/notification_service.dart';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await Injector.setup(appConfig: AppConfig.dev());
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: HydratedStorageDirectory(
        (await getApplicationDocumentsDirectory()).path),
  );
  await NotificationService.initialize(AppConfig.dev());
  await SentryFlutter.init(
    (options) {
      options.dsn =
          'https://<EMAIL>/3';
      options.environment = "development";
      options.tracesSampleRate = 1.0;
    },
    appRunner: () => runApp(const BuyerBoardApp()),
  );
}
