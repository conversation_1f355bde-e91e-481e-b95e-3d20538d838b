import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

@immutable
class LocationEntity extends Equatable {
  final String zipCode;
  final String cityName;
  final String stateId;
  final String stateName;
  final double latitude;
  final double longitude;

  const LocationEntity({
    required this.zipCode,
    required this.cityName,
    required this.stateId,
    required this.stateName,
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object?> get props =>
      [zipCode, cityName];
}
