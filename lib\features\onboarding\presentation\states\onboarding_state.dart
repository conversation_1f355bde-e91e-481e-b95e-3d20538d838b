import 'package:freezed_annotation/freezed_annotation.dart';

import '../../data/onboarding_model.dart';
part 'onboarding_state.freezed.dart';

@freezed
class OnBoardingState with _$OnBoardingState {
  const factory OnBoardingState.initial() = initial;
  const factory OnBoardingState.loading() = loading;
  const factory OnBoardingState.success(List<OnboardingModel> onBoardings) =
      success;
  const factory OnBoardingState.onBoardingError(String? error) =
      onBoardingError;
}
