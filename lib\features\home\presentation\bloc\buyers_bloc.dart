import 'package:buyer_board/common/models/wrapper.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/add_buyer/domain/repository/buyer_repository.dart';
import 'package:buyer_board/features/filters/data/filters_respository.dart';
import 'package:buyer_board/features/filters/presentation/bloc/new_filter_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BuyersBloc extends Bloc<BuyersEvent, BuyersState> {
  BuyersBloc({
    required this.buyerRepository,
    required this.filtersRepository,
  }) : super(BuyersLoading()) {
    on<LoadBuyers>(_onLoadBuyers);
    on<ClearList>(_onClearList);
    on<UpdateBuyer>(_onUpdateBuyer);
  }

  final BuyerRepository buyerRepository;
  final FiltersRepository filtersRepository;

  List<BuyerModel> allBuyers = [];
  int currentPage = 1;
  bool isLoadMore = false;
  bool hasReachedMax = false;

  void _onClearList(ClearList event, Emitter<BuyersState> emit) {
    allBuyers = [];
    currentPage = 1;
    hasReachedMax = false;
  }

  void _onLoadBuyers(LoadBuyers event, Emitter<BuyersState> emit) async {
    if (event.forceRefresh || event.page != null) {
      emit(BuyersLoading());
      currentPage = event.page ?? 1;
      allBuyers.clear();
      hasReachedMax = false;
    }
    if (isLoadMore || hasReachedMax) return;
    isLoadMore = true;
    try {
      final filterData = event.context.read<NewFilterCubit>().state.copyWith(
            page: Wrapped.value(currentPage),
          );
      final response = await filtersRepository.filteredBuyers(filterData);
      allBuyers.addAll(response.data);
      if (response.pagination.currentPage >= response.pagination.totalPages) {
        hasReachedMax = true;
      }
      emit(BuyersLoaded(allBuyers, hasReachedMax: hasReachedMax));
      currentPage = currentPage + 1;
    } catch (e) {
      emit(BuyersError(e.toString()));
    } finally {
      isLoadMore = false;
    }
  }

  void _onUpdateBuyer(UpdateBuyer event, Emitter<BuyersState> emit) {
    final index =
        allBuyers.indexWhere((element) => element.id == event.buyer.id);
    if (index != -1) {
      allBuyers[index] = event.buyer;
      emit(BuyersLoaded(allBuyers, hasReachedMax: hasReachedMax));
    }
  }
}
