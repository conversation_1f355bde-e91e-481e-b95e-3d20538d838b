import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/resources/drawables.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/onboarding/presentation/cubit/onboarding_cubit.dart';
import 'package:buyer_board/features/splash/presentation/cubit/splash_cubit.dart';
import 'package:buyer_board/features/splash/presentation/state/splash_screen_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    context.read<OnBoardingCubit>().initOnboarding();
    context.read<SplashCubit>().initSplash();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.sizeOf(context).width;
    return BlocListener<SplashCubit, SplashScreenState>(
      listener: (context, state) {
        state.mapOrNull(
          localAuth: (state) => context.go(PagePath.authScreen, extra: true),
          success: (state) => context.go(state.route),
        );
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: AppColors.primary,
        body: Padding(
          padding: const EdgeInsets.all(24),
          child: SafeArea(
            child: SizedBox(
              width: double.maxFinite,
              child: Column(
                children: [
                  Expanded(
                    child: SizedBox(
                      // height: deviceWidth * 0.7,
                      width: deviceWidth * 0.7,
                      child: SvgPicture.asset(
                        Drawables.bbLogoWithTm,
                      ),
                    ),
                  ),
                  // const SizedBox(height: 32),
                  // Text(
                  //   Strings.appName,
                  //   style: AppStyles.largeBold4x.copyWith(
                  //     color: AppColors.white,
                  //     fontSize: deviceWidth * 0.1,
                  //   ),
                  // ),
                  // Text(
                  //   Strings.splashHeadline,
                  //   style: AppStyles.largeSemiBold.copyWith(
                  //     color: AppColors.white,
                  //     fontSize: deviceWidth * 0.05,
                  //   ),
                  // ),
                  const Expanded(
                    child: Center(
                      child: CupertinoActivityIndicator(
                        color: AppColors.white,
                        radius: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
