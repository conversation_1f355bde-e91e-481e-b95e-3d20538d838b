<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <queries>
        <!-- If your app checks for email support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="mailto" />
        </intent>
    </queries>
    <!-- The INTERNET permission is required for development. Specifically,
         the Flutter tool needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.

    -->
    <uses-permission android:name="android.permission.INTERNET"/>
</manifest>
