import 'package:buyer_board/common/widgets/common_text_form_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/resources/resources.dart';
import '../cubit/buyer_info_cubit.dart';

class AdditionalRequestsSection extends StatefulWidget {
  const AdditionalRequestsSection({super.key, this.sync = false});
  final bool sync;

  @override
  State<AdditionalRequestsSection> createState() =>
      _AdditionalRequestsSectionState();
}

class _AdditionalRequestsSectionState extends State<AdditionalRequestsSection> {
  final List<TextEditingController> desireControllers = [];
  final typicalRequests = {
    'Pool': false,
    'Spa': false,
    'View': false,
    'One-Level': false,
    'Tennis Court': false,
    'Fixer': false,
  };
  final Map<String, bool> additionalRequests = {};
  @override
  void initState() {
    if (widget.sync) {
      syncAdditionalDesires();
    }
    super.initState();
  }

  void syncAdditionalDesires() {
    final additionalDesires =
        context.read<BuyerInfoCubit>().state.additionalRequests;
    for (var desire in additionalDesires) {
      if (typicalRequests.keys.contains(desire)) {
        typicalRequests[desire] = true;
      } else {
        additionalRequests[desire] = true;
      }
    }
  }

  void addRequestToBloc(String request) {
    context.read<BuyerInfoCubit>().addAdditionalRequestItem(request);
  }

  void removeRequestFromBloc(String request) {
    context.read<BuyerInfoCubit>().removeAdditionalRequestItem(request);
  }

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.additionalRequests,
          style: typography.large1xBlack,
        ),
        Text(
          'Buyer requested features. Select all that apply.',
          style: typography.mediumReg,
        ),
        spacerH16,
        Column(
          children: typicalRequests.entries
              .map(
                (entry) => _CheckboxTile(
                  value: entry.value,
                  label: entry.key,
                  onChanged: (selected) {
                    if (selected == null) return;
                    if (selected) {
                      addRequestToBloc(entry.key);
                    } else {
                      removeRequestFromBloc(entry.key);
                    }
                    setState(() {
                      typicalRequests[entry.key] = selected;
                    });
                  },
                ),
              )
              .toList(),
        ),
        Divider(
          color: context.appColors.greyXLightGreyM,
          thickness: 1,
          height: 32,
        ),
        _AdditionalRequestSection(
          desireControllers: desireControllers,
          additionalRequests: additionalRequests,
        ),
      ],
    );
  }
}

class _CheckboxTile extends StatelessWidget {
  const _CheckboxTile({
    super.key,
    required this.value,
    required this.label,
    required this.onChanged,
    this.onRemove,
  });
  final bool value;
  final String label;
  final void Function(bool?) onChanged;
  final void Function()? onRemove;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            onChanged(!value);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Checkbox(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: const VisualDensity(
                  horizontal: VisualDensity.minimumDensity,
                  vertical: -1.5,
                ),
                value: value,
                onChanged: onChanged,
              ),
              spacerW8,
              Text(
                label,
                style: context.typography.mediumReg,
              ),
            ],
          ),
        ),
        const Spacer(),
        if (onRemove != null)
          IconButton(
            onPressed: onRemove,
            style: ButtonStyle(
              padding: WidgetStateProperty.all(EdgeInsets.zero),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            ),
            icon: Icon(
              Icons.close,
              color: context.appColors.blackWhite,
            ),
          ),
      ],
    );
  }
}

class _AdditionalRequestSection extends StatefulWidget {
  final List<TextEditingController> desireControllers;
  final Map<String, bool> additionalRequests;
  const _AdditionalRequestSection(
      {required this.desireControllers, required this.additionalRequests});

  @override
  State<_AdditionalRequestSection> createState() =>
      _AdditionalRequestSectionState();
}

class _AdditionalRequestSectionState extends State<_AdditionalRequestSection> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final Map<int, String?> _errorMessages = {};

  void addRequestField() {
    final controller = TextEditingController();
    widget.desireControllers.add(controller);
    setState(() {});
  }

  void addRequestToBloc(String request) {
    context.read<BuyerInfoCubit>().addAdditionalRequestItem(request);
  }

  void removeRequestFromBloc(String request) {
    context.read<BuyerInfoCubit>().removeAdditionalRequestItem(request);
  }

  void onRemove(int index) {
    widget.desireControllers.removeAt(index);
    setState(() {});
  }

  void _validateDuplicate(TextEditingController controller) {
    final allValues =
        widget.desireControllers.map((c) => c.text.trim()).toList();
    final blocSavedValues =
        context.read<BuyerInfoCubit>().state.additionalRequests.toList();
    setState(() {
      for (int i = 0; i < widget.desireControllers.length; i++) {
        final value = widget.desireControllers[i].text.trim();
        if (value.isNotEmpty &&
            (allValues.where((v) => v == value).length > 1 ||
                blocSavedValues.contains(value))) {
          _errorMessages[i] = 'Duplicate entry detected';
        } else {
          _errorMessages[i] = null;
        }
      }
    });
  }

  void saveRequest(int index) {
    final currentValue = widget.desireControllers[index].text.trim();
    final blocSavedValues =
        context.read<BuyerInfoCubit>().state.additionalRequests.toList();
    final isDuplicate = widget.desireControllers
                .where((c) => c.text.trim() == currentValue)
                .length >
            1 ||
        blocSavedValues.contains(currentValue);
    if (isDuplicate) {
      setState(() {
        _errorMessages[index] = 'Duplicate entry detected';
      });
      return;
    }
    context.read<BuyerInfoCubit>().addAdditionalRequestItem(currentValue);
    widget.additionalRequests[currentValue] = true;
    Future.microtask(() {
      setState(() {
        widget.desireControllers.removeAt(index);
      });
    });
  }

  @override
  void dispose() {
    for (var controller in widget.desireControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final xColors = context.appColors;
    final colors = context.colorScheme;
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Add additional requests by tapping "+Add Additional Request" below.',
            style: typography.mediumReg,
          ),
          spacerH16,
          Text(
            "Additional Requests (20 Characters Limit Per Entry)",
            style: typography.smallReg.copyWith(
              color: xColors.greyM,
            ),
          ),
          spacerH16,
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final entry = widget.additionalRequests.entries.elementAt(index);
              return Container(
                padding: const EdgeInsets.only(bottom: 5.0),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: context.appColors.blackWhite,
                      width: 1.0,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        entry.key,
                        style: typography.largeReg.copyWith(
                          color: context.appColors.blackWhite,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        removeRequestFromBloc(entry.key);
                        setState(() {
                          widget.additionalRequests.remove(entry.key);
                        });
                      },
                      child: Icon(
                        Icons.close_sharp,
                        color: context.appColors.blackWhite,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              );
            },
            separatorBuilder: (ctx, _) => const SizedBox(height: 8),
            itemCount: widget.additionalRequests.length,
          ),
          spacerH8,
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonTextFormField(
                  controller: widget.desireControllers[index],
                  fillColor: Colors.transparent,
                  onChanged: (value) {
                    _validateDuplicate(widget.desireControllers[index]);
                  },
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(20),
                    FilteringTextInputFormatter.deny(RegExp(r'^\s+')),
                  ],
                  hint: 'Additional Request',
                  textCapitalization: TextCapitalization.sentences,
                  contextTextColor: colors.onSurface,
                  labelStyle:
                      typography.smallReg.copyWith(color: xColors.greyM),
                  hintTextStyle: typography.largeReg.copyWith(
                    color: xColors.greyLightGreyDefault,
                  ),
                  contextTextStyle: typography.largeReg,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  borderColor: colors.outline,
                  suffixIcon: InkWell(
                    onTap: () {
                      if (widget.desireControllers[index].text.trim().isEmpty) {
                        setState(() {
                          _errorMessages[index] = 'Field cannot be empty';
                        });
                        return;
                      }
                      saveRequest(index);
                    },
                    child: Icon(
                      Icons.check,
                      color: xColors.pPXLight,
                    ),
                  ),
                ),
                if (_errorMessages[index] != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      _errorMessages[index]!,
                      style: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            ),
            separatorBuilder: (ctx, _) => const SizedBox(height: 8),
            itemCount: widget.desireControllers.length,
          ),
          TextButton.icon(
            onPressed: addRequestField,
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
            ),
            label: const Text('Add Additional Request'),
            icon: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }
}
