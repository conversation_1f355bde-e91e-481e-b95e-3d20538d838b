import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:flutter/material.dart';

class CommonRadioTile<T> extends StatelessWidget {
  const CommonRadioTile({
    super.key,
    required this.label,
    required this.onChange,
    required this.value,
    required this.groupValue,
  });
  final String label;
  final T value;
  final T groupValue;
  final void Function(T?) onChange;

  @override
  Widget build(BuildContext context) {
    // final colorScheme = context.theme.colorScheme;
    final xColors = context.appColors;
    return InkWell(
      onTap: () {
        onChange(value);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Radio(
            activeColor: xColors.pPXLight,
            value: value,
            groupValue: groupValue,
            onChanged: onChange,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            visualDensity: const VisualDensity(
              horizontal: VisualDensity.minimumDensity,
              vertical: -2.5,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: context.typography.mediumReg.copyWith(
              color: value == groupValue
                  ? xColors.pPXLight
                  : xColors.blackGreyMedium,
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }
}
