import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/common/widgets/common_text_form_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/core/utils/validators.dart';
import 'package:buyer_board/features/forget_password/presentation/cubit/reset_passsord_cubit.dart';
import 'package:buyer_board/features/splash/presentation/widgets/logo_and_headline_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../state/reset_password_state.dart';

class ForgetPasswordScreen extends StatefulWidget {
  const ForgetPasswordScreen({super.key});
  @override
  State<ForgetPasswordScreen> createState() => _ForgetPasswordScreenState();
}

class _ForgetPasswordScreenState extends State<ForgetPasswordScreen> {
  final emailTextController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  void onSendMeTheCode() {
    context
        .read<ResetPasswordCubit>()
        .requestOtp(email: emailTextController.text.trim());
  }

  @override
  void dispose() {
    emailTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ResetPasswordCubit, ResetPasswordState>(
      listener: (context, state) => state.maybeMap(
          loading: (state) => Loader.show(),
          otpSent: (state) => {
                Loader.hide(),
                context
                  ..showToast(isSuccess: true, message: state.message)
                  ..push(
                    PagePath.pinCodeScreen,
                    extra: emailTextController.text,
                  ),
              },
          resetPasswordError: (state) => {
                Loader.hide(),
                context.showToast(isSuccess: false, message: state.error),
              },
          orElse: () => Loader.hide()),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: AppColors.primary,
        body: Padding(
          padding: const EdgeInsets.all(24),
          child: SafeArea(
            child: SizedBox(
              width: double.maxFinite,
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const LogoAndHeadlineSection(),
                    const SizedBox(height: 60),
                    Text(
                      Strings.forgetPasswordMessage,
                      textAlign: TextAlign.center,
                      style: context.typography.mediumReg.copyWith(
                        color: context.appColors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    CommonTextFormField(
                      label: 'Email Address',
                      // hint: Strings.emailHint,
                      controller: emailTextController,
                      hintTextStyle: context.typography.largeReg.copyWith(
                        color: AppColors.white,
                      ),
                      contextTextColor: AppColors.white,
                      validator: Validators().emailValidator,
                    ),
                    const Spacer(),
                    CommonButton.basic(
                      label: Strings.sendMeTheCode,
                      action: () {
                        if (_formKey.currentState?.validate() ?? false) {
                          onSendMeTheCode();
                        }
                      },
                      backgroundColor: AppColors.white,
                      textColor: AppColors.primary,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
