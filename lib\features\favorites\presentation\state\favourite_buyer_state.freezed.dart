// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'favourite_buyer_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FavouriteBuyerState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavouriteBuyerStateCopyWith<$Res> {
  factory $FavouriteBuyerStateCopyWith(
          FavouriteBuyerState value, $Res Function(FavouriteBuyerState) then) =
      _$FavouriteBuyerStateCopyWithImpl<$Res, FavouriteBuyerState>;
}

/// @nodoc
class _$FavouriteBuyerStateCopyWithImpl<$Res, $Val extends FavouriteBuyerState>
    implements $FavouriteBuyerStateCopyWith<$Res> {
  _$FavouriteBuyerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$FavouriteBuyerStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'FavouriteBuyerState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements FavouriteBuyerState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$loadingImplCopyWith<$Res> {
  factory _$$loadingImplCopyWith(
          _$loadingImpl value, $Res Function(_$loadingImpl) then) =
      __$$loadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$loadingImplCopyWithImpl<$Res>
    extends _$FavouriteBuyerStateCopyWithImpl<$Res, _$loadingImpl>
    implements _$$loadingImplCopyWith<$Res> {
  __$$loadingImplCopyWithImpl(
      _$loadingImpl _value, $Res Function(_$loadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$loadingImpl implements loading {
  const _$loadingImpl();

  @override
  String toString() {
    return 'FavouriteBuyerState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$loadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class loading implements FavouriteBuyerState {
  const factory loading() = _$loadingImpl;
}

/// @nodoc
abstract class _$$toggleInProgressImplCopyWith<$Res> {
  factory _$$toggleInProgressImplCopyWith(_$toggleInProgressImpl value,
          $Res Function(_$toggleInProgressImpl) then) =
      __$$toggleInProgressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$toggleInProgressImplCopyWithImpl<$Res>
    extends _$FavouriteBuyerStateCopyWithImpl<$Res, _$toggleInProgressImpl>
    implements _$$toggleInProgressImplCopyWith<$Res> {
  __$$toggleInProgressImplCopyWithImpl(_$toggleInProgressImpl _value,
      $Res Function(_$toggleInProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$toggleInProgressImpl implements toggleInProgress {
  const _$toggleInProgressImpl();

  @override
  String toString() {
    return 'FavouriteBuyerState.toggleInProgress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$toggleInProgressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) {
    return toggleInProgress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) {
    return toggleInProgress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) {
    if (toggleInProgress != null) {
      return toggleInProgress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) {
    return toggleInProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) {
    return toggleInProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) {
    if (toggleInProgress != null) {
      return toggleInProgress(this);
    }
    return orElse();
  }
}

abstract class toggleInProgress implements FavouriteBuyerState {
  const factory toggleInProgress() = _$toggleInProgressImpl;
}

/// @nodoc
abstract class _$$successImplCopyWith<$Res> {
  factory _$$successImplCopyWith(
          _$successImpl value, $Res Function(_$successImpl) then) =
      __$$successImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<BuyerModel> buyers, String message});
}

/// @nodoc
class __$$successImplCopyWithImpl<$Res>
    extends _$FavouriteBuyerStateCopyWithImpl<$Res, _$successImpl>
    implements _$$successImplCopyWith<$Res> {
  __$$successImplCopyWithImpl(
      _$successImpl _value, $Res Function(_$successImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyers = null,
    Object? message = null,
  }) {
    return _then(_$successImpl(
      buyers: null == buyers
          ? _value._buyers
          : buyers // ignore: cast_nullable_to_non_nullable
              as List<BuyerModel>,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$successImpl implements success {
  const _$successImpl(
      {required final List<BuyerModel> buyers, required this.message})
      : _buyers = buyers;

  final List<BuyerModel> _buyers;
  @override
  List<BuyerModel> get buyers {
    if (_buyers is EqualUnmodifiableListView) return _buyers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_buyers);
  }

  @override
  final String message;

  @override
  String toString() {
    return 'FavouriteBuyerState.success(buyers: $buyers, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$successImpl &&
            const DeepCollectionEquality().equals(other._buyers, _buyers) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_buyers), message);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      __$$successImplCopyWithImpl<_$successImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) {
    return success(buyers, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) {
    return success?.call(buyers, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(buyers, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class success implements FavouriteBuyerState {
  const factory success(
      {required final List<BuyerModel> buyers,
      required final String message}) = _$successImpl;

  List<BuyerModel> get buyers;
  String get message;

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$toggleSuccessImplCopyWith<$Res> {
  factory _$$toggleSuccessImplCopyWith(
          _$toggleSuccessImpl value, $Res Function(_$toggleSuccessImpl) then) =
      __$$toggleSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BuyerModel buyer, String message});
}

/// @nodoc
class __$$toggleSuccessImplCopyWithImpl<$Res>
    extends _$FavouriteBuyerStateCopyWithImpl<$Res, _$toggleSuccessImpl>
    implements _$$toggleSuccessImplCopyWith<$Res> {
  __$$toggleSuccessImplCopyWithImpl(
      _$toggleSuccessImpl _value, $Res Function(_$toggleSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyer = null,
    Object? message = null,
  }) {
    return _then(_$toggleSuccessImpl(
      buyer: null == buyer
          ? _value.buyer
          : buyer // ignore: cast_nullable_to_non_nullable
              as BuyerModel,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$toggleSuccessImpl implements toggleSuccess {
  const _$toggleSuccessImpl({required this.buyer, required this.message});

  @override
  final BuyerModel buyer;
  @override
  final String message;

  @override
  String toString() {
    return 'FavouriteBuyerState.toggleSuccess(buyer: $buyer, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$toggleSuccessImpl &&
            (identical(other.buyer, buyer) || other.buyer == buyer) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, buyer, message);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$toggleSuccessImplCopyWith<_$toggleSuccessImpl> get copyWith =>
      __$$toggleSuccessImplCopyWithImpl<_$toggleSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) {
    return toggleSuccess(buyer, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) {
    return toggleSuccess?.call(buyer, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) {
    if (toggleSuccess != null) {
      return toggleSuccess(buyer, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) {
    return toggleSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) {
    return toggleSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) {
    if (toggleSuccess != null) {
      return toggleSuccess(this);
    }
    return orElse();
  }
}

abstract class toggleSuccess implements FavouriteBuyerState {
  const factory toggleSuccess(
      {required final BuyerModel buyer,
      required final String message}) = _$toggleSuccessImpl;

  BuyerModel get buyer;
  String get message;

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$toggleSuccessImplCopyWith<_$toggleSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$favouriteBuyersErrorImplCopyWith<$Res> {
  factory _$$favouriteBuyersErrorImplCopyWith(_$favouriteBuyersErrorImpl value,
          $Res Function(_$favouriteBuyersErrorImpl) then) =
      __$$favouriteBuyersErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$favouriteBuyersErrorImplCopyWithImpl<$Res>
    extends _$FavouriteBuyerStateCopyWithImpl<$Res, _$favouriteBuyersErrorImpl>
    implements _$$favouriteBuyersErrorImplCopyWith<$Res> {
  __$$favouriteBuyersErrorImplCopyWithImpl(_$favouriteBuyersErrorImpl _value,
      $Res Function(_$favouriteBuyersErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$favouriteBuyersErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$favouriteBuyersErrorImpl implements favouriteBuyersError {
  const _$favouriteBuyersErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'FavouriteBuyerState.favouriteBuyersError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$favouriteBuyersErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$favouriteBuyersErrorImplCopyWith<_$favouriteBuyersErrorImpl>
      get copyWith =>
          __$$favouriteBuyersErrorImplCopyWithImpl<_$favouriteBuyersErrorImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) {
    return favouriteBuyersError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) {
    return favouriteBuyersError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) {
    if (favouriteBuyersError != null) {
      return favouriteBuyersError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) {
    return favouriteBuyersError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) {
    return favouriteBuyersError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) {
    if (favouriteBuyersError != null) {
      return favouriteBuyersError(this);
    }
    return orElse();
  }
}

abstract class favouriteBuyersError implements FavouriteBuyerState {
  const factory favouriteBuyersError(final String? error) =
      _$favouriteBuyersErrorImpl;

  String? get error;

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$favouriteBuyersErrorImplCopyWith<_$favouriteBuyersErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$toggleErrorImplCopyWith<$Res> {
  factory _$$toggleErrorImplCopyWith(
          _$toggleErrorImpl value, $Res Function(_$toggleErrorImpl) then) =
      __$$toggleErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$toggleErrorImplCopyWithImpl<$Res>
    extends _$FavouriteBuyerStateCopyWithImpl<$Res, _$toggleErrorImpl>
    implements _$$toggleErrorImplCopyWith<$Res> {
  __$$toggleErrorImplCopyWithImpl(
      _$toggleErrorImpl _value, $Res Function(_$toggleErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$toggleErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$toggleErrorImpl implements toggleError {
  const _$toggleErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'FavouriteBuyerState.toggleError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$toggleErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$toggleErrorImplCopyWith<_$toggleErrorImpl> get copyWith =>
      __$$toggleErrorImplCopyWithImpl<_$toggleErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() toggleInProgress,
    required TResult Function(List<BuyerModel> buyers, String message) success,
    required TResult Function(BuyerModel buyer, String message) toggleSuccess,
    required TResult Function(String? error) favouriteBuyersError,
    required TResult Function(String? error) toggleError,
  }) {
    return toggleError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? toggleInProgress,
    TResult? Function(List<BuyerModel> buyers, String message)? success,
    TResult? Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult? Function(String? error)? favouriteBuyersError,
    TResult? Function(String? error)? toggleError,
  }) {
    return toggleError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? toggleInProgress,
    TResult Function(List<BuyerModel> buyers, String message)? success,
    TResult Function(BuyerModel buyer, String message)? toggleSuccess,
    TResult Function(String? error)? favouriteBuyersError,
    TResult Function(String? error)? toggleError,
    required TResult orElse(),
  }) {
    if (toggleError != null) {
      return toggleError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(toggleInProgress value) toggleInProgress,
    required TResult Function(success value) success,
    required TResult Function(toggleSuccess value) toggleSuccess,
    required TResult Function(favouriteBuyersError value) favouriteBuyersError,
    required TResult Function(toggleError value) toggleError,
  }) {
    return toggleError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(toggleInProgress value)? toggleInProgress,
    TResult? Function(success value)? success,
    TResult? Function(toggleSuccess value)? toggleSuccess,
    TResult? Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult? Function(toggleError value)? toggleError,
  }) {
    return toggleError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(toggleInProgress value)? toggleInProgress,
    TResult Function(success value)? success,
    TResult Function(toggleSuccess value)? toggleSuccess,
    TResult Function(favouriteBuyersError value)? favouriteBuyersError,
    TResult Function(toggleError value)? toggleError,
    required TResult orElse(),
  }) {
    if (toggleError != null) {
      return toggleError(this);
    }
    return orElse();
  }
}

abstract class toggleError implements FavouriteBuyerState {
  const factory toggleError(final String? error) = _$toggleErrorImpl;

  String? get error;

  /// Create a copy of FavouriteBuyerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$toggleErrorImplCopyWith<_$toggleErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
