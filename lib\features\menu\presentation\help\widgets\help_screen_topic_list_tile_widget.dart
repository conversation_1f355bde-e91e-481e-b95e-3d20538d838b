import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/menu/data/models/help_response.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class TopicListTileWidget extends StatelessWidget {
  const TopicListTileWidget({super.key, required this.help});
  final HelpResponse help;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.push(PagePath.topic, extra: help),
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    help.title,
                    style: context.typography.largeSemi,
                  ),
                ),
                spacerW8,
                Icon(
                  Icons.arrow_forward_ios,
                  size: Dimensions.materialPadding,
                  color: context.appColors.greyM,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
