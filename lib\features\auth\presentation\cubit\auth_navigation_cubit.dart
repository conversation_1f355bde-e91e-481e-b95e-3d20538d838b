import 'package:buyer_board/features/auth/presentation/state/auth_navigation_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

final class UserAuthRouteCubit extends Cubit<UserAuthRouteState> {
  UserAuthRouteCubit() : super(UserAuthRouteStateLogin());

  void changeNavigationRoute(UserAuthRouteState state) {
    emit(state);
  }

  void invalidate() => emit(UserAuthRouteStateLogin());
}
