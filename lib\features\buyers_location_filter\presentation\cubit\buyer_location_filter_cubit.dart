import 'dart:async';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/usecases/get_filter_locations.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/usecases/get_search_locations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

sealed class BuyerLocationFilterState {}

class BuyerLocationFilterInitial extends BuyerLocationFilterState {}

class BuyerLocationFilterLoading extends BuyerLocationFilterState {}

class BuyerLocationFilterError extends BuyerLocationFilterState {
  final String message;

  BuyerLocationFilterError(this.message);
}

class BuyerLocationFilterLoaded extends BuyerLocationFilterState {
  final List<LocationEntity> locations;
  BuyerLocationFilterLoaded(this.locations);
}

class BuyerFilterLocationsCubit extends Cubit<BuyerLocationFilterState> {
  final GetFilterLocationsUseCase getFilterLocations;
  final SearchLocationsByZipCodeUseCase searchLocationsByZipCode;

  BuyerFilterLocationsCubit(
      this.getFilterLocations, this.searchLocationsByZipCode)
      : super(BuyerLocationFilterInitial());

  List<LocationEntity> _locations = [];
  int _currentPage = 1;
  final int _pageSize = 100;
  bool _isFetching = false;
  bool _hasMoreData = true;
    Timer? _debounce;

  void getLocations({bool loadNextPage = false}) async {
    if (_isFetching || (loadNextPage && !_hasMoreData)) return;

    if (!loadNextPage) {
      emit(BuyerLocationFilterLoading());
    }

    if (!loadNextPage) {
      // Reset pagination when not loading the next page
      _currentPage = 1;
      _locations.clear();
      _hasMoreData = true;
    }

    _isFetching = true;
    try {
      // Fetch the paginated locations
      final newLocations = await getFilterLocations(
        page: _currentPage,
        pageSize: _pageSize,
      );
      _locations.addAll(newLocations);
      _hasMoreData = newLocations.length == _pageSize;
      if (_hasMoreData) {
        _currentPage++;
      }

      emit(BuyerLocationFilterLoaded(_locations));
    } catch (e) {
      emit(BuyerLocationFilterError(e.toString()));
    } finally {
      _isFetching = false;
    }
  }

  bool _isSearching = false;
void searchLocations(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(seconds: 1), () async {
      if (_isSearching) return;

      // Reset pagination variables
      _currentPage = 1;
      _hasMoreData = false;
      _isSearching = true;

      try {
        final searchResults = await searchLocationsByZipCode(value);
        _locations.clear();
        _locations.addAll(searchResults);
        emit(BuyerLocationFilterLoaded(_locations));
      } catch (e) {
        emit(BuyerLocationFilterError(e.toString()));
      } finally {
        _isSearching = false;
      }
    });
  }


 @override
  Future<void> close() {
    _debounce?.cancel();
    return super.close();
  }
}
