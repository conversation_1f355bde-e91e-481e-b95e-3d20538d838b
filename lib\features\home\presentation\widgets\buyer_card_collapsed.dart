import 'dart:ui';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/favourite_buyers_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import '../../../add_buyer/data/models/buyer_model.dart';

class BuyerCardCollapsed extends StatefulWidget {
  const BuyerCardCollapsed({
    super.key,
    required this.buyer,
    this.showNotes = false,
    this.onFavouriteToggled,
  });
  final BuyerModel buyer;
  final bool showNotes;
  final VoidCallback? onFavouriteToggled;
  @override
  State<BuyerCardCollapsed> createState() => _BuyerCardCollapsedState();
}

class _BuyerCardCollapsedState extends State<BuyerCardCollapsed> {
  bool loading = false;
  late BuyerModel _buyer;
  Gradient _getImageOverlay() {
    final colors = context.colorScheme;
    final isMyBuyer = widget.buyer.myBuyer;
    final color = isMyBuyer ? colors.primary : colors.inverseSurface;
    return LinearGradient(
      colors: [
        color,
        color.withOpacity(0.3),
      ],
      stops: const [0.3, 0.7],
      begin: AlignmentDirectional.centerStart,
      end: AlignmentDirectional.centerEnd,
    );
  }

  String getThumbXImagePath(String propertyType) {
    return "assets/images/png/$propertyType.png";
  }

  String getBudget() {
    final budgetStr = double.tryParse(widget.buyer.buyerNeeds?.budget ?? "0");
    if (budgetStr == null) return "";
    final budget = '\$${CoreUtils.formatLargeNumber(budgetStr)}';
    final isRent = widget.buyer.buyerNeeds?.purchaseType == PurchaseType.lease;
    return isRent ? "$budget/mo" : budget;
  }

  @override
  void initState() {
    _buyer = widget.buyer;
    super.initState();
  }

  Future<void> onFavouriteToggle() async {
    final isFav = _buyer.isFavourite;
    setState(() {
      loading = true;
    });
    await context
        .read<FavouriteBuyersCubit>()
        .toggleFavourite(buyerId: widget.buyer.id!, isFavourite: !isFav)
        .then((val) {
      if (mounted) {
        setState(() {
          _buyer = _buyer.copyWith(isFavourite: !isFav);
          loading = false;
        });
      }
      context.read<FavouriteBuyersCubit>().getFavouriteBuyers(silent: true);
    }).onError((err, _) {
      if (mounted) {
        setState(() {
          loading = false;
          _buyer = _buyer.copyWith(isFavourite: isFav);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    final overlayColor =
        widget.buyer.myBuyer ? colors.primary : colors.inverseSurface;
    return Stack(
      alignment: Alignment.topRight,
      children: [
        Positioned(
          child: Container(
            width: double.infinity,
            height: 166,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                fit: BoxFit.cover,
                image: AssetImage(
                  widget.buyer.propertyImage,
                ),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 2.5, sigmaY: 2.5),
                child: Container(
                  decoration:
                      BoxDecoration(color: Colors.white.withOpacity(0.0)),
                ),
              ),
            ),
          ),
        ),
        Positioned(
          child: Container(
            width: double.infinity,
            height: 166,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              gradient: _getImageOverlay(),
            ),
          ),
        ),
        Column(
          children: [
            InkWell(
              onTap: () => context.push(
                PagePath.expandedBuyerCard,
                extra: _buyer,
              ),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: Dimensions.padding_10,
                  horizontal: Dimensions.materialPadding_2x,
                ).copyWith(right: 4),
                height: 166,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.buyer.sku ?? 'B1357',
                      style: context.typography.large1xBlack
                          .copyWith(color: colors.surface),
                    ),
                    Text(
                      widget.buyer.buyersAlias ?? "",
                      style: context.typography.mediumReg.copyWith(
                        color: colors.onPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 36,
                      child: ListView(
                        shrinkWrap: true,
                        scrollDirection: Axis.horizontal,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(18),
                              color: colors.surface,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 4),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  PropertyAttributeIcon(
                                    iconPath: Drawables.icBedroomFilledBlack,
                                    value: widget.buyer.buyerNeeds?.minBedrooms
                                            .toString() ??
                                        "",
                                    color: overlayColor,
                                  ),
                                  const SizedBox(width: 8),
                                  PropertyAttributeIcon(
                                    iconPath: Drawables.icBathroomFilledBlack,
                                    value: widget.buyer.buyerNeeds?.minBathrooms
                                            .toString() ??
                                        "",
                                    color: overlayColor,
                                  ),
                                  const SizedBox(width: 8),
                                  PropertyAttributeIcon(
                                    iconPath: Drawables.icAreaFilledBlack,
                                    value: CoreUtils.formatLargeNumber(
                                        widget.buyer.buyerNeeds as num),
                                    color: overlayColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(18),
                              color: colors.surface,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 4),
                              child: Text(
                                getBudget(),
                                style: AppStyles.mediumSemiBold.copyWith(
                                  color: overlayColor,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        Positioned(
          child: Container(
            width: 46,
            height: 46,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.2),
              borderRadius: const BorderRadiusDirectional.only(
                bottomStart: Radius.circular(8),
                topEnd: Radius.circular(8),
              ),
            ),
            child: InkWell(
              onTap: () async {
                await onFavouriteToggle();
                if (widget.onFavouriteToggled != null) {
                  widget.onFavouriteToggled!();
                }
              },
              child: Center(
                child: loading
                    ? const CupertinoActivityIndicator(
                        color: Colors.white,
                      )
                    : Icon(
                        _buyer.isFavourite
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: Colors.white,
                      ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class PropertyAttributeIcon extends StatelessWidget {
  const PropertyAttributeIcon({
    super.key,
    required this.iconPath,
    required this.value,
    required this.color,
  });
  final String iconPath;
  final String value;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          iconPath,
          colorFilter: ColorFilter.mode(
            color,
            BlendMode.srcIn,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: AppStyles.mediumSemiBold.copyWith(
            color: color,
          ),
        ),
      ],
    );
  }
}
