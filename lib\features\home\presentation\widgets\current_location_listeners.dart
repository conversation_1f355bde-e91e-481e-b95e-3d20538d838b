import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filter_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/device_current_location_cubit.dart';
import 'package:buyer_board/features/filters/presentation/bloc/new_filter_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:buyer_board/features/profile/presentation/states/profile_state.dart';
import 'package:buyer_board/features/settings/presentation/cubit/share_location_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

import '../../../buyers_location_filter/presentation/cubit/buyer_location_filtered_value_cubit.dart';

class CurrentLocationListeners extends StatefulWidget {
  const CurrentLocationListeners({super.key, required this.child});

  final Widget child;

  @override
  State<CurrentLocationListeners> createState() =>
      _CurrentLocationListenersState();
}

class _CurrentLocationListenersState extends State<CurrentLocationListeners> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ProfileCubit, ProfileState>(listener: (context, state) {
          final isSharing = context.read<ShareLocationCubit>().state;

          state.mapOrNull(
            success: (state) {
              final user = state.user;
              if (isSharing) return;
              final currentLocationState =
                  context.read<CurrentLocationCubit>().state;
              if (currentLocationState is LocationDataState) {
                final currentLocation = currentLocationState.location;
                if (currentLocation?.zipCode != null) return;
              }
              final zipCode = user.profile?.brokerageZipCode;
              if (zipCode != null && zipCode.isNotEmpty) {
                context.read<CurrentLocationCubit>().update(
                      LocationEntity(
                        zipCode: zipCode,
                        cityName: user.profile?.brokerageCity ?? '',
                        stateName: user.profile?.brokerageState ?? '',
                        latitude: -1,
                        longitude: -1,
                        stateId: '',
                      ),
                    );
              }
            },
          );
        }),
        BlocListener<DeviceCurrentLocationCubit, DeviceCurrentLocationState>(
            listener: (context, state) {
          if (state is DeviceCurrentLocationLoaded &&
              state.location.zipCode.isNotEmpty) {
            context.read<CurrentLocationCubit>().update(state.location);
          }
        }),
        BlocListener<CurrentLocationCubit, LocationState>(
          listener: (context, state) async {
            if (state is LocationDataState) {
              context
                  .read<NewFilterCubit>()
                  .setSearchZipCode(state.location?.zipCode);
              Future.microtask(
                () => context.read<BuyersBloc>().add(
                      LoadBuyers(forceRefresh: true, context: context),
                    ),
              );
              // Remove previous tags for zipCode
              await OneSignal.User.removeTag('zipCode');
              Future.microtask(() async {
                OneSignal.User.addTagWithKey(
                    'zipCode', state.location?.zipCode);
              });
            }
          },
        ),
      ],
      child: _InitState(child: widget.child),
    );
  }
}

class _InitState extends StatefulWidget {
  const _InitState({
    required this.child,
  });

  final Widget child;

  @override
  State<_InitState> createState() => __InitStateState();
}

class __InitStateState extends State<_InitState> {
  @override
  void initState() {
    super.initState();
    context.read<BuyerFilterLocationsCubit>().getLocations();
    _fetchLocation();
  }

  void _fetchLocation() async {
    final locationSharing = context.read<ShareLocationCubit>().state;
    if (locationSharing) {
      final location = await context.read<DeviceCurrentLocationCubit>().fetch();
      if (location != null && location.zipCode.isNotEmpty) {
        if (mounted) {
          context.read<CurrentLocationCubit>().update(location);
        }
      } else {
        _fetchProfileLocationData();
      }
    } else {
      _fetchProfileLocationData();
    }
  }

  void _fetchProfileLocationData() {
    final user = context.read<ProfileCubit>().getUserProfile();
    final zipCode = user?.profile?.brokerageZipCode;

    if (zipCode != null && zipCode.isNotEmpty) {
      context.read<CurrentLocationCubit>().update(
            LocationEntity(
              zipCode: zipCode,
              cityName: user!.profile?.brokerageCity ?? '',
              stateName: user.profile?.brokerageState ?? '',
              latitude: -1,
              longitude: -1,
              stateId: '',
            ),
          );
    } else {
      context.read<CurrentLocationCubit>().update(null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ShareLocationCubit, bool>(
          listener: (context, sharing) {
            _fetchLocation();
          },
        ),
      ],
      child: widget.child,
    );
  }
}
