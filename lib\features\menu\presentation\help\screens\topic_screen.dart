import 'package:buyer_board/common/widgets/app_bar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/menu/data/models/help_response.dart';
import 'package:buyer_board/features/menu/presentation/help/widgets/expansion_collapse_buttons_widget.dart';
import 'package:buyer_board/features/menu/presentation/help/widgets/like_and_unlike_buttons_widget.dart';
import 'package:buyer_board/features/menu/presentation/help/widgets/topic_category_expansion_tile.dart';
import 'package:flutter/material.dart';

class TopicScreen extends StatelessWidget {
  const TopicScreen({super.key, required this.help});
  final HelpResponse help;

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final xColors = context.appColors;
    return Scaffold(
      appBar: ApplicationAppBar.buildAppBar(
        context,
        title: Strings.topic,
        leadingWidget: const SizedBox.shrink(),
        actions: [
          TextButton(
            child: Text(
              Strings.done,
              style: typography.largeReg.copyWith(
                color: xColors.whitePXLight,
              ),
            ),
            onPressed: () => context.shouldPop(),
          )
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(Dimensions.materialPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              help.title,
              style: typography.largeSemi,
            ),
            const SizedBox(
              height: Dimensions.padding_4,
            ),
            Text(
              help.description,
              textAlign: TextAlign.justify,
              style: typography.mediumReg.copyWith(
                color: xColors.greyDefaultGreyMedium,
              ),
            ),
            const SizedBox(
              height: Dimensions.materialPadding,
            ),
            const ExpansionCollapseButtonsWidget(),
            spacerH12,
            ...List.generate(help.category.length, (index) {
              final category = help.category[index];
              return TopicCategoryExpansionTile(category: category);
            }),
            const SizedBox(
              height: Dimensions.materialPadding,
            ),
            Text(
              Strings.wasThisHelpful,
              style: typography.mediumSemi,
            ),
            spacerH8,
            const LikeAndUnlikeButtonsWidget(),
            const SizedBox(
              height: Dimensions.padding_8,
            ),
            const Divider(),
            Text(
              Strings.needMoreHelp,
              style: typography.large1xSemi,
            ),
            Text(
              Strings.trySendingUsMessage,
              style: typography.mediumSemi,
            ),
            const SizedBox(
              height: Dimensions.materialPadding,
            ),
            const _SendUsAMessageButton(),
            const SizedBox(
              height: Dimensions.padding_8,
            ),
            const Divider(),
            const SizedBox(
              height: Dimensions.materialPadding,
            ),
            Text(
              Strings.relatedTopics,
              style: AppStyles.mediumSemiBold.copyWith(
                color: context.colorScheme.onSurface,
              ),
            ),
            // ...List.generate(help.topics.length, (index) {
            //   final topic = help.topics[index];
            //   return TopicCategoryExpansionTile(category: category);
            // })
          ],
        ),
      ),
    );
  }
}

class _SendUsAMessageButton extends StatelessWidget {
  const _SendUsAMessageButton({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(4),
      child: Ink(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        height: 44,
        decoration: BoxDecoration(
          color: context.appColors.pPXLight,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              Strings.sendUsAMessage,
              style: context.typography.mediumSemi
                  .copyWith(color: context.appColors.whitePXDark),
            ),
            Icon(
              Icons.arrow_forward,
              size: 20,
              color: context.appColors.whitePXDark,
            )
          ],
        ),
      ),
    );
  }
}
