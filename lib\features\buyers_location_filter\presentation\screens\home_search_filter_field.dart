import 'package:buyer_board/common/widgets/app_slide_transition.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filtered_value_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_search_slide_animation_type_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/screens/location_filter_list_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class HomeSearchFilterField extends StatefulWidget {
  const HomeSearchFilterField({super.key});

  @override
  State<HomeSearchFilterField> createState() => HomeSearchFilterFieldState();
}

class HomeSearchFilterFieldState extends State<HomeSearchFilterField> {
  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colors.surface,
        boxShadow: [
          BoxShadow(
            color: colors.shadow.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      height: 41,
      child: BlocBuilder<CurrentLocationCubit, LocationState>(
        builder: (context, state) {
          return switch (state) {
            LocationLoadingState() => const _LocationField(
                isLoading: true,
              ),
            LocationDataState(location: LocationEntity? location) =>
              _LocationField(selectedLocation: location),
          };
        },
      ),
    );
  }
}

class _LocationField extends StatelessWidget {
  const _LocationField({
    super.key,
    this.selectedLocation,
    this.isLoading = false,
  });

  final LocationEntity? selectedLocation;
  final bool isLoading;

  void showBottomSheet(BuildContext context, LocationEntity? selectedLocation) {
    if (!context.mounted) return;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      backgroundColor: context.theme.appColors.whitePDark,
      builder: (_) => SizedBox(
        height: MediaQuery.sizeOf(context).height * 0.9,
        child: LocationFilterListScreen(
          selectedLocation: selectedLocation,
          onLocationSelected: (location) {
            context.read<BuyerSearchSlideAnimationTypeCubit>().changeType(
                  context: context,
                  newLocation: location,
                  oldLocation: selectedLocation ?? location,
                );
            context.read<CurrentLocationCubit>().update(location);
            context.pop();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final noLocation =
        selectedLocation == null || selectedLocation!.zipCode.isEmpty;
    final colors = context.colorScheme;
    return InkWell(
      onTap: () => showBottomSheet(context, selectedLocation),
      borderRadius: BorderRadius.circular(4),
      child: Container(
        decoration: BoxDecoration(
          color: noLocation && !isLoading
              ? colors.error
              : colors.secondaryContainer,
          borderRadius: BorderRadius.circular(4),
        ),
        height: 33,
        child: isLoading
            ? const Center(
                child: CupertinoActivityIndicator(),
              )
            : ClipRRect(
                borderRadius: BorderRadius.circular(4),
                clipBehavior: Clip.antiAlias,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    BlocBuilder<BuyerSearchSlideAnimationTypeCubit,
                        AppSlideTransitionType>(
                      builder: (context, type) {
                        return SizedBox(
                          child: AppSlideTransition(
                            type: type,
                            child: FittedBox(
                              child: Row(
                                key: ValueKey(selectedLocation),
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    size: 24,
                                    color: noLocation
                                        ? context.theme.appColors.white
                                        : colors.onSecondaryContainer,
                                  ),
                                  spacerW8,
                                  if (!noLocation) ...[
                                    Text(
                                      selectedLocation!.zipCode,
                                      style: context.typography.largeBlack
                                          .copyWith(
                                        color: colors.onSecondaryContainer,
                                        fontSize: 22,
                                      ),
                                    ),
                                    spacerW8,
                                    Text(
                                      selectedLocation!.cityName,
                                      style:
                                          context.typography.largeReg.copyWith(
                                        color: context
                                            .theme.appColors.pXLightPLight,
                                        fontSize: 22,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                  if (noLocation)
                                    Text(
                                      'No Location Detected',
                                      style:
                                          context.typography.largeReg.copyWith(
                                        color: context.theme.appColors.white,
                                        fontSize: 22,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    // const Spacer(),
                    // if (selectedLocation != null)
                    //   IconButton(
                    //     style: IconButton.styleFrom(
                    //       padding: EdgeInsets.zero,
                    //       tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    //     ),
                    //     onPressed: () {
                    //       context.read<NewFilterCubit>().setSearchZipCode(null);
                    //       context.read<CurrentLocationCubit>().update(null);
                    //     },
                    //     icon: Icon(
                    //       Icons.close,
                    //       color: colors.onSecondaryContainer,
                    //     ),
                    //   ),
                  ],
                ),
              ),
      ),
    );
  }
}
