import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:flutter/material.dart';
import '../../../../common/widgets/common_radio_tile.dart';
import '../../../../core/resources/resources.dart';
import '../cubit/buyer_info_cubit.dart';

class FinancialStatusWidget extends StatefulWidget {
  const FinancialStatusWidget({super.key});

  @override
  State<FinancialStatusWidget> createState() => _FinancialStatusWidgetState();
}

class _FinancialStatusWidgetState extends State<FinancialStatusWidget> {
  void toggleFinancialStatus(FinancialStatus? val) {
    FinancialStatus? financialStatus = financialStatusNotifier.value;
    val != null && financialStatus != val
        ? financialStatusNotifier.value = val
        : financialStatusNotifier.value = null;
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: financialStatusNotifier,
      builder: (context, financialStatus, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.financialStatus,
              style: context.typography.smallReg.copyWith(
                color: context.appColors.greyM,
              ),
            ),
            const SizedBox(height: 16),
            ...List.generate(FinancialStatus.values.length, (index) {
              final status = FinancialStatus.values[index];
              return Column(
                children: [
                  CommonRadioTile<FinancialStatus?>(
                    label: status.label,
                    onChange: toggleFinancialStatus,
                    value: status,
                    groupValue: financialStatus,
                  ),
                  const SizedBox(height: 2),
                ],
              );
            }),
            ValueListenableBuilder(
                valueListenable: financialStatusError,
                builder: (context, financialStatus, _) {
                  return financialStatusError.value!.isNotEmpty
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 2),
                            Container(
                              height: 1,
                              width: double.infinity,
                              color: AppColors.errorMedium,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 4.0),
                              child: Text(
                                "Field Required",
                                style: AppStyles.small.copyWith(
                                    color: AppColors.errorMedium,
                                    fontSize: 11,
                                    fontWeight: FontWeight.w700),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox();
                })
          ],
        );
      },
    );
  }
}
