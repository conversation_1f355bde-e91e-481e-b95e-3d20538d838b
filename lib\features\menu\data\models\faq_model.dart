
import 'package:json_annotation/json_annotation.dart';
part 'faq_model.g.dart';

@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)
class FaqModel{
  FaqModel({
    required this.question
});
  final  List<FaqQuestionData> question;

  factory FaqModel.fromJson(Map<String, dynamic> json) =>
      _$FaqModelFromJson(json);
}


@JsonSerializable(createToJson: false, fieldRename: FieldRename.snake)

class FaqQuestionData {
  FaqQuestionData({
    required this.description,
    required this.title
});
  final String title;
  final String description;

  factory FaqQuestionData.fromJson(Map<String, dynamic> json) =>
      _$FaqQuestionDataFromJson(json);
}

