import 'dart:developer' as developer;

import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/network/interceptors/error_handler_interceptor.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/auth/data/models/requests/apple_login_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/social_auth_request.dart';
import 'package:buyer_board/features/auth/domain/repository/auth_repository.dart';
import 'package:flutter/material.dart';
import 'package:buyer_board/features/auth/presentation/state/social_auth_state.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class SocialAuthCubit extends Cubit<SocialAuthState> {
  final AppPreferences appPreferences;
  final AuthRepository authRepository;
  SocialAuthCubit({
    required this.appPreferences,
    required this.authRepository,
  }) : super(const SocialAuthState.initial());
  final _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
    ],
  );
  Future<void> socialAuth(BuildContext context,
      {required String email,
      required String firstName,
      required String lastName,
      required String provider}) async {
    try {
      emit(const SocialAuthState.loading());

      final userResponse = await authRepository.socialAuth(
        socialAuthRequest: SocialAuthRequest(
          email: email,
          provider: provider,
          firstName: firstName,
          lastName: lastName,
        ),
      );
      if (userResponse.data != null) {
        appPreferences.setUser(userResponse.data!);
        context.read<UserSessionCubit>().setUser(userResponse.data!);
        emit(SocialAuthState.socialAuthSuccess(userResponse.data));
      } else {
        developer.log('Social auth failed: No data in response',
            name: 'SocialAuth');
        emit(const SocialAuthState.socialAuthError(
            'Something went wrong. Please try again later'));
      }
    } on ErrorObjectException catch (e, stackTrace) {
      developer.log('Social auth error: ${e.toString()}',
          name: 'SocialAuth', error: e, stackTrace: stackTrace);
      emit(SocialAuthState.socialAuthError(e.toString()));
    } catch (e, stackTrace) {
      developer.log('Social auth unexpected error: ${e.toString()}',
          name: 'SocialAuth', error: e, stackTrace: stackTrace);
      emit(SocialAuthState.socialAuthError(e.toString()));
    }
  }

  Future authenticateWithGoogle(BuildContext context) async {
    try {
      final user = await _googleSignIn.signIn();
      if (user != null) {
        String? fullName = user.displayName;
        List<String> nameParts = fullName?.split(' ') ?? [];
        String firstName = nameParts.isNotEmpty ? nameParts[0] : '';
        String lastName = nameParts.length > 1 ? nameParts.last : '';
        socialAuth(
          context,
          email: user.email,
          firstName: firstName,
          lastName: lastName,
          provider: "google",
        );
      } else {
        developer.log('User cancelled Google Sign In', name: 'GoogleAuth');
      }
    } on PlatformException catch (e, stackTrace) {
      developer.log(
          'Google Sign In PlatformException: '
          'Code: ${e.code}, '
          'Message: ${e.message}, '
          'Details: ${e.details}',
          name: 'GoogleAuth',
          error: e,
          stackTrace: stackTrace);
      emit(SocialAuthState.socialAuthError(
          'Failed to sign in with Google. Please try again.'));
    } catch (e, stackTrace) {
      developer.log('Google Sign In Error: ${e.toString()}',
          name: 'GoogleAuth', error: e, stackTrace: stackTrace);
      emit(SocialAuthState.socialAuthError(e.toString()));
    }
  }

  Future<void> authenticateWithApple(BuildContext context) async {
    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // If email is null, proceed with only userIdentifier
      final email = credential.email ?? '';
      final appleIdentifier = credential.userIdentifier!;
      final firstName = credential.givenName;
      final lastName = credential.familyName;
      // developer.log(credential.toString(), name: 'credential:----------------------');

      final response = await authRepository.loginWithApple(
        requestModel: AppleLoginRequest(
          firstName: firstName,
          lastName: lastName,
          email: email, // Handle null email
          appleIdentifier: appleIdentifier,
          provider: 'apple',
        ),
      );

      if (response.data != null) {
        // Store user info and set user session
        appPreferences.setUser(response.data!);
        context.read<UserSessionCubit>().setUser(response.data!);

        emit(SocialAuthState.socialAuthSuccess(response.data));
      } else {
        emit(const SocialAuthState.socialAuthError(
          'Something went wrong. Please try again later',
        ));
      }
    } catch (exception, stackTrace) {
      // Check if the error is due to the user canceling the login
      if (exception is SignInWithAppleAuthorizationException &&
          exception.code == AuthorizationErrorCode.canceled) {
        // User canceled, do not show error
        debugPrint('Apple Sign-In canceled by user.');
      } else {
        await Sentry.captureException(
          exception,
          stackTrace: stackTrace,
        );
        // Handle other errors
        debugPrint(exception.toString());
        emit(SocialAuthState.socialAuthError(
            'An error occurred: ${exception.toString()}'));
      }
    }
  }

  void logOut() async {
    await authRepository.logOut();
    try {
      if (await _googleSignIn.isSignedIn()) {
        _googleSignIn.signOut();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    appPreferences.clearAppPreferences();
  }
}
