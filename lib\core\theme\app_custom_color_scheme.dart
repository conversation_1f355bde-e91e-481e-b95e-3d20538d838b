import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/theme/app_color_extension.dart';

abstract final class AppCustomColorScheme {
  static AppColorsExtension get light => _lightAppColors;
  static AppColorsExtension get dark => _darkAppColors;
}

final _lightAppColors = AppColorsExtension(
  pXLightPLight: AppColors.primaryXLight,
  greyXLightPDark: AppColors.greyXLight,
  greyLightPDark: AppColors.greyLight,
  whitePDark: AppColors.white,
  greyLightPDefault: AppColors.greyLight,
  whitePXLight: AppColors.white,
  blackWhite: AppColors.black,
  pPXLight: AppColors.primary,
  gBlackGLight: AppColors.black,
  whiteBlack: AppColors.white,
  whitePXDark: AppColors.white,
  greyM: AppColors.greyMedium,
  error: AppColors.error,
  black: AppColors.black,
  greyLightGreyDark: AppColors.greyLight,
  pLightPMedium: AppColors.primaryLight,
  greyLightGreyDefault: AppColors.greyLight,
  blackGreyMedium: AppColors.black,
  greyMediumGreyDefault: AppColors.greyMedium,
  greyDefaultGreyMedium: AppColors.grey,
  greyLightPXDark: AppColors.greyLight,
  greyXLightPXDark: AppColors.greyXLight,
  greyMP: AppColors.greyMedium,
  pLightGrey: AppColors.primaryLight,
  greyXLightGreyM: AppColors.greyXLight,
  white: AppColors.white,
  pBlack: AppColors.primary,
  pLight: AppColors.primaryLight,
  greyMGreyL: AppColors.greyMedium,
);

final _darkAppColors = AppColorsExtension(
  pXLightPLight: AppColors.primaryLight,
  greyXLightPDark: AppColors.primaryDark,
  greyLightPDark: AppColors.primaryDark,
  whitePDark: AppColors.primaryDark,
  greyLightPDefault: AppColors.primary,
  whitePXLight: AppColors.primaryXLight,
  blackWhite: AppColors.white,
  pPXLight: AppColors.primaryXLight,
  gBlackGLight: AppColors.greyLight,
  whiteBlack: AppColors.black,
  whitePXDark: AppColors.primaryXDark,
  greyM: AppColors.greyMedium,
  error: AppColors.error,
  black: AppColors.black,
  greyLightGreyDark: AppColors.greyDark,
  pLightPMedium: AppColors.primaryMedium,
  greyLightGreyDefault: AppColors.grey,
  blackGreyMedium: AppColors.greyMedium,
  greyMediumGreyDefault: AppColors.grey,
  greyDefaultGreyMedium: AppColors.greyMedium,
  greyLightPXDark: AppColors.primaryXDark,
  greyXLightPXDark: AppColors.primaryXDark,
  greyMP: AppColors.primary,
  pLightGrey: AppColors.grey,
  greyXLightGreyM: AppColors.greyMedium,
  white: AppColors.white,
  pBlack: AppColors.black,
  pLight: AppColors.primaryLight,
  greyMGreyL: AppColors.greyLight,
);
