import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:equatable/equatable.dart';

sealed class ChatEvent extends Equatable {
  const ChatEvent();
}

class GetChat extends ChatEvent {
  final ChatPayload payload;
  const GetChat(this.payload);
  @override
  List<Object> get props => [payload];
}

class SendMessage extends ChatEvent {
  final ChatPayload payload;
  const SendMessage(this.payload);

  @override
  List<Object> get props => [payload];
}

class DeleteMessages extends ChatEvent {
  final ChatPayload payload;
  const DeleteMessages(this.payload);

  @override
  List<Object> get props => [payload];
}

class CloseChatConnection extends ChatEvent {
  const CloseChatConnection();

  @override
  List<Object> get props => [];
}

class RetrySendMessage extends ChatEvent {
  final int id;
  final ChatPayload payload;
  const RetrySendMessage(this.id, this.payload);

  @override
  List<Object> get props => [id, payload];
}

class DiscardFailedMessage extends ChatEvent {
  final int id;
  const DiscardFailedMessage(this.id);

  @override
  List<Object> get props => [id];
}
