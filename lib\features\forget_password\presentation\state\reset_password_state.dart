import 'package:freezed_annotation/freezed_annotation.dart';
part 'reset_password_state.freezed.dart';

@freezed
class ResetPasswordState with _$ResetPasswordState {
  const factory ResetPasswordState.initial() = initial;
  const factory ResetPasswordState.loading() = loading;
  const factory ResetPasswordState.otpSent(String message) = otpSent;
  const factory ResetPasswordState.wrongOtp(String error) = wrongOtp;
  const factory ResetPasswordState.otpVerified(String message) = otpVerified;
  const factory ResetPasswordState.passwordUpdated(String message) =
      passwordUpdated;
  const factory ResetPasswordState.resetPasswordError(String? error) =
      resetPasswordError;
  const factory ResetPasswordState.otpVerificationError(String? error) =
      otpVerificationError;
  const factory ResetPasswordState.updatePasswordError(String? error) =
      updatePasswordError;
}
