import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:flutter/material.dart';

class ApplicationBar extends StatelessWidget implements PreferredSizeWidget {
  const ApplicationBar({
    super.key,
    required this.title,
    this.subtitle,
  });

  final String title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final colors = context.appColors;
    return AppBar(
      title: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            title,
            style: typography.largeSemi.copyWith(
              color: colors.whitePXLight,
            ),
          ),
          if (subtitle != null)
            Text(
              subtitle!,
              style: typography.small1xReg.copyWith(color: colors.pLight),
            ),
        ],
      ),
      leading: Icon<PERSON>utton(
        icon: const Icon(
          Icons.arrow_back_ios,
          size: 25,
        ),
        color: colors.whitePXLight,
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(56.0);
}
