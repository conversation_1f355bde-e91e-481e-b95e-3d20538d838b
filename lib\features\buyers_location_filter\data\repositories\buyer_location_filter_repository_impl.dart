import 'package:buyer_board/core/network/rest_api_client.dart';
import 'package:buyer_board/features/buyers_location_filter/data/models/location_model.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';

import '../../domain/repositories/buyer_location_filter_repository.dart';

class BuyerLocationFilterRepositoryImpl
    implements BuyerLocationFilterRepository {
  final RestAPIClient restAPIClient;

  BuyerLocationFilterRepositoryImpl({required this.restAPIClient});

  @override
  Future<List<LocationModel>> getLocations(
      {required int page, required int pageSize}) async {
    final response = await restAPIClient.getFilterLocations(
      page: page,
      pageSize: pageSize,
    );
    return response.data!;
  }

  @override
  Future<List<LocationModel>> searchLocationsByZipCode(
      {required String search}) async {
    final response = await restAPIClient.getSearchLocations(
      search: search,
    );
    return response.data!;
  }

  @override
  Future<LocationEntity> getCurrentLocation() {
    try {
      return _determinePosition().then((position) {
        return _fetchLocationEntity(position.latitude, position.longitude)
            .then((locationEntity) {
          if (locationEntity != null) {
            return locationEntity;
          } else {
            return Future.error('Location not found');
          }
        });
      });
    } catch (e) {
      return Future.error(e);
    }
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }
    return await Geolocator.getCurrentPosition();
  }

  Future<LocationEntity?> _fetchLocationEntity(
      double latitude, double longitude) async {
    try {
      List<Placemark> placemarks =
          await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark placemark = placemarks.first;
        // final zipCode = placemark.postalCode == null || placemark.postalCode!.isEmpty ? '35446' : placemark.postalCode!;
        return LocationEntity(
          zipCode: placemark.postalCode ?? '',
          cityName: placemark.locality ?? '',
          stateId: placemark.administrativeArea ??
              '', // Assuming stateId can be the state abbreviation or full name
          stateName: placemark.administrativeArea ?? '',
          latitude: latitude,
          longitude: longitude,
        );
      }
    } catch (e) {
      // Handle the error appropriately
    }
    return null;
  }
}
