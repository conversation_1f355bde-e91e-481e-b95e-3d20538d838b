// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'buyer_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BuyerModelResponse _$BuyerModelResponseFromJson(Map<String, dynamic> json) =>
    BuyerModelResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => BuyerModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      pagination:
          Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BuyerModelResponseToJson(BuyerModelResponse instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'pagination': instance.pagination.toJson(),
    };

Pagination _$PaginationFromJson(Map<String, dynamic> json) => Pagination(
      totalPages: (json['total_pages'] as num).toInt(),
      totalEntries: (json['total_entries'] as num).toInt(),
      pageSize: (json['page_size'] as num).toInt(),
      currentPage: (json['current_page'] as num).toInt(),
    );

Map<String, dynamic> _$PaginationToJson(Pagination instance) =>
    <String, dynamic>{
      'total_pages': instance.totalPages,
      'total_entries': instance.totalEntries,
      'page_size': instance.pageSize,
      'current_page': instance.currentPage,
    };

BuyerModel _$BuyerModelFromJson(Map<String, dynamic> json) => BuyerModel(
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
      imageUrl: json['image_url'] as String?,
      timeOffset: (json['time_offset'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      buyersAlias: json['buyers_alias'] as String?,
      email: json['email'] as String?,
      primaryPhoneNumber: json['primary_phone_number'] as String?,
      optionalPhoneNumber: json['optional_phone_number'] as String?,
      buyerLocationsOfInterest:
          (json['buyer_locations_of_interest'] as List<dynamic>?)
                  ?.map((e) => e as String)
                  .toList() ??
              const [],
      buyerNeeds: json['buyer_need'] == null
          ? null
          : BuyerNeeds.fromJson(json['buyer_need'] as Map<String, dynamic>),
      isFavourite: json['is_favourite'] as bool? ?? false,
      notes: json['note'] as String?,
      additionalRequests: (json['additional_requests'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      buyerExpirationDate: json['buyer_expiration_date'] == null
          ? null
          : DateTime.parse(json['buyer_expiration_date'] as String),
      insertedAt: json['inserted_at'] == null
          ? null
          : DateTime.parse(json['inserted_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      myBuyer: json['my_buyer'] as bool? ?? false,
      sku: json['sku'] as String?,
    );

Map<String, dynamic> _$BuyerModelToJson(BuyerModel instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('id', instance.id);
  writeNotNull('user', instance.user?.toJson());
  writeNotNull('image_url', instance.imageUrl);
  writeNotNull('time_offset', instance.timeOffset);
  writeNotNull('first_name', instance.firstName);
  writeNotNull('last_name', instance.lastName);
  writeNotNull('buyers_alias', instance.buyersAlias);
  writeNotNull('email', instance.email);
  writeNotNull('primary_phone_number', instance.primaryPhoneNumber);
  writeNotNull('optional_phone_number', instance.optionalPhoneNumber);
  val['buyer_locations_of_interest'] = instance.buyerLocationsOfInterest;
  writeNotNull('buyer_need', instance.buyerNeeds?.toJson());
  val['is_favourite'] = instance.isFavourite;
  writeNotNull('note', instance.notes);
  val['additional_requests'] = instance.additionalRequests;
  writeNotNull(
      'buyer_expiration_date', instance.buyerExpirationDate?.toIso8601String());
  writeNotNull('inserted_at', instance.insertedAt?.toIso8601String());
  writeNotNull('updated_at', instance.updatedAt?.toIso8601String());
  writeNotNull('sku', instance.sku);
  val['my_buyer'] = instance.myBuyer;
  return val;
}

BuyerNeeds _$BuyerNeedsFromJson(Map<String, dynamic> json) => BuyerNeeds(
      purchaseType:
          $enumDecodeNullable(_$PurchaseTypeEnumMap, json['purchase_type']),
      propertyType:
          $enumDecodeNullable(_$PropertyTypeEnumMap, json['property_type']),
      financialStatus: $enumDecodeNullable(
          _$FinancialStatusEnumMap, json['financial_status']),
      budget: json['budget_upto'] as String?,
      minBedrooms: (json['min_bedrooms'] as num?)?.toDouble(),
      minBathrooms: (json['min_bathrooms'] as num?)?.toDouble(),
      minArea: (json['min_area'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$BuyerNeedsToJson(BuyerNeeds instance) =>
    <String, dynamic>{
      'purchase_type': _$PurchaseTypeEnumMap[instance.purchaseType],
      'property_type': _$PropertyTypeEnumMap[instance.propertyType],
      'financial_status': _$FinancialStatusEnumMap[instance.financialStatus],
      'budget_upto': instance.budget,
      'min_bedrooms': instance.minBedrooms,
      'min_bathrooms': instance.minBathrooms,
      'min_area': instance.minArea,
    };

const _$PurchaseTypeEnumMap = {
  PurchaseType.purchase: 'purchase',
  PurchaseType.lease: 'lease',
};

const _$PropertyTypeEnumMap = {
  PropertyType.singleFamily: 'single_family_house',
  PropertyType.townhouse: 'townhouse',
  PropertyType.condo: 'condo',
  PropertyType.apartment: 'apartment',
  PropertyType.multiFamily: 'multi_family_house',
  PropertyType.land: 'land',
};

const _$FinancialStatusEnumMap = {
  FinancialStatus.preQualified: 'pre_qualified',
  FinancialStatus.preApproved: 'pre_approved',
  FinancialStatus.allCash: 'all_cash',
  FinancialStatus.nA: 'n_a',
};
