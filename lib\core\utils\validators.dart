import 'package:buyer_board/core/resources/strings.dart';

class Validators {
  Validators();
  String? emailValidator(String? email) {
    if (email == null || email.trim().isEmpty) {
      return Strings.emailRequiredValidator;
    } else {
      return RegExp(
                  r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
              .hasMatch(
        email.trim(),
      )
          ? null
          : Strings.emailValidator;
    }
  }

  String? passwordValidator(String? password) {
    if (password == null || password.isEmpty) {
      return Strings.passwordRequiredValidator;
    }
    if (password.length < 6) {
      return Strings.passwordValidator;
    }
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      return "Password must contain at least one uppercase letter.";
    }
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      return "Password must contain at least one lowercase letter.";
    }
    if (!RegExp(r'\d').hasMatch(password)) {
      return "Password must contain at least one digit.";
    }
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return "Password must contain at least one special character.";
    }
    return null;
  }

  String? requiredFieldValidator(String? val) {
    if (val == null || val.trim().isEmpty) {
      return Strings.requiredFieldValidator;
    } else {
      return null;
    }
  }

  String? zipcodeFieldValidator(String? val) {
    if (val == null || val.trim().isEmpty) {
      return Strings.requiredFieldValidator;
    } else if (val.length < 5) {
      return 'Invalid Zip code';
    }
    return null;
  }

  String? requiredBrokageNameValidator(String? val) {
    if (val == null || val.trim().isEmpty) {
      return Strings.brokerageNameFieldValidator;
    } else {
      return null;
    }
  }
}
