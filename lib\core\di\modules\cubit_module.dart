// import 'package:buyer_board/features/auth/presentation/bloc/auth_cubit.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/buyer_info_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/login_with_email_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/logout_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/register_with_email_password_cubit.dart';
import 'package:buyer_board/features/auth/presentation/cubit/social_auth_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filter_cubit.dart';
import 'package:buyer_board/features/forget_password/presentation/cubit/reset_passsord_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/menu/cubit/intro_to_buyer_board_cubit.dart';
import 'package:buyer_board/features/menu/presentation/faq/cubit/faq_screen_cubit.dart';
import 'package:buyer_board/features/menu/presentation/help/cubit/help_screen_cubit.dart';
import 'package:buyer_board/features/onboarding/presentation/cubit/onboarding_cubit.dart';
import 'package:buyer_board/features/profile/presentation/cubit/address_cubit.dart';
import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:buyer_board/features/splash/presentation/cubit/splash_cubit.dart';
import 'package:kiwi/kiwi.dart';

import '../../../features/add_buyer/presentation/cubit/add_buyer_cubit.dart';
import '../../../features/favorites/presentation/cubit/favourite_buyers_cubit.dart';
import '../../../features/favorites/presentation/cubit/note_favorite_cubit.dart';

abstract class CubitAndBlocModule {
  static late KiwiContainer _container;

  static void setup(KiwiContainer container) {
    _container = container;
    _registerUserSessionCubit();
    _registerSplashCubit();
    _registerSocialAuthCubit();
    _registerLoginWithEmailCubit();
    _registerSignUpWithEmailCubit();
    _registerOnBoardingCubit();
    _registerIntroToBuyerBoardCubit();
    _registerHelpScreenCubit();
    _registerResetPasswordCubit();
    _registerProfileCubit();
    _registerAddBuyerCubit();
    _registerBuyerInfoCubit();
    _registerFavouriteBuyersCubit();
    _registerNoteFavoriteCubit();
    _registerFaqScreenCubit();
    _registerLogoutCubit();
    _registerBuyersBloc();
    _registerAddressCubit();
    _buyerFilterLocationsCubit();
  }

  static void _registerUserSessionCubit() {
    _container.registerSingleton(
      (_) => UserSessionCubit(),
    );
  }

  static void _registerSplashCubit() {
    _container.registerSingleton(
      (_) => SplashCubit(appPreferences: _container.resolve()),
    );
  }

  static void _registerSocialAuthCubit() {
    _container.registerSingleton((_) => SocialAuthCubit(
          authRepository: _container.resolve(),
          appPreferences: _container.resolve(),
        ));
  }

  static void _registerLoginWithEmailCubit() {
    _container.registerSingleton((_) => LoginWithEmailCubit(
          authRepository: _container.resolve(),
          appPreferences: _container.resolve(),
          localAuth: _container.resolve(),
        ));
  }

  static void _registerSignUpWithEmailCubit() {
    _container.registerSingleton((_) => SignUpWithEmailCubit(
          localAuth: _container.resolve(),
          authRepository: _container.resolve(),
          appPreferences: _container.resolve(),
        ));
  }

  static void _registerOnBoardingCubit() {
    _container.registerSingleton((_) => OnBoardingCubit());
  }

  static void _registerIntroToBuyerBoardCubit() {
    _container.registerSingleton((_) => IntroToBuyerBoardCubit());
  }

  static void _registerResetPasswordCubit() {
    _container.registerSingleton((_) =>
        ResetPasswordCubit(resetPasswordRepository: _container.resolve()));
  }

  static void _registerProfileCubit() {
    _container.registerSingleton(
      (_) => ProfileCubit(
        userRepository: _container.resolve(),
        appPreferences: _container.resolve(),
        userSession: _container.resolve(),
      ),
    );
  }

  static void _registerAddBuyerCubit() {
    _container.registerSingleton(
      (_) => AddBuyerCubit(
          buerRepository: _container.resolve(),
          userRepository: _container.resolve()),
    );
  }

  static void _registerBuyerInfoCubit() {
    _container.registerSingleton(
      (_) => BuyerInfoCubit(),
    );
  }

  static void _registerFavouriteBuyersCubit() {
    _container.registerSingleton(
      (_) => FavouriteBuyersCubit(buyerRepository: _container.resolve()),
    );
  }

  static void _registerNoteFavoriteCubit() {
    _container.registerSingleton(
      (_) => NoteFavoriteCubit(_container.resolve()),
    );
  }

  static void _registerHelpScreenCubit() {
    _container.registerSingleton((_) => HelpScreenCubit());
  }

  static void _registerLogoutCubit() {
    _container.registerSingleton((_) => LogoutCubit(
          authRepository: _container.resolve(),
          appPreferences: _container.resolve(),
        ));
  }

  static void _registerFaqScreenCubit() {
    _container.registerSingleton((_) => FaqScreenCubit());
  }

  static void _registerBuyersBloc() {
    _container.registerSingleton(
      (_) => BuyersBloc(
        buyerRepository: _container.resolve(),
        filtersRepository: _container.resolve(),
      ),
    );
  }

  static void _registerAddressCubit() {
    _container.registerSingleton(
      (_) => AddressCubit(userRepository: _container.resolve()),
    );
  }

  // static void _buyerFilterLocationsCubit() {
  //   _container.registerSingleton(
  //     (_) => BuyerFilterLocationsCubit(
  //       _container.resolve(),
  //     ),
  //   );
  // }


  static void _buyerFilterLocationsCubit() {
  _container.registerSingleton(
    (_) => BuyerFilterLocationsCubit(
      _container.resolve(),
      _container.resolve(),
    ),
  );
}



}
