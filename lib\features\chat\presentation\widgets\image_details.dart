import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player/video_player.dart';

class AttachmentDetails extends StatefulWidget {
  final List<String> imagePaths;
  final List<ChatMessage> messages;
  final int userId;
  final int otherUserId;
  final int buyerId;
  const AttachmentDetails({
    super.key,
    required this.imagePaths,
    required this.userId,
    required this.otherUserId,
    required this.buyerId,
    required this.messages,
  });

  @override
  _AttachmentDetailsState createState() => _AttachmentDetailsState();
}

class _AttachmentDetailsState extends State<AttachmentDetails> {
  int selectedIndex = 0;
  late PageController _pageController;
  final Map<int, VideoPlayerController> _videoControllers = {};
  TextEditingController messageController = TextEditingController();
  bool _isMessageEmpty = true;

  @override
  void initState() {
    super.initState();
    parentMessageId = widget.messages[0].id!;
    _pageController = PageController(initialPage: selectedIndex);
    for (int i = 0; i < widget.imagePaths.length; i++) {
      if (_isVideo(widget.imagePaths[i])) {
        _videoControllers[i] =
            VideoPlayerController.networkUrl(Uri.parse(widget.imagePaths[i]))
              ..initialize().then((_) {
                setState(() {});
              });
        _videoControllers[i]!.addListener(() {
          if (_videoControllers[i]!.value.position ==
              _videoControllers[i]!.value.duration) {
            setState(() {
              _videoControllers[i]!.seekTo(Duration.zero);
            });
          }
        });
      }
    }
    messageController.addListener(() {
      setState(() {
        _isMessageEmpty = messageController.text.trim().isEmpty;
      });
    });
  }

  int? parentMessageId;

  @override
  void dispose() {
    _pageController.dispose();
    messageController.dispose();
    for (var controller in _videoControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  bool _isVideo(String url) {
    final videoExtensions = ['mp4', 'mov', 'avi', 'mkv'];
    final extension = url.split('.').last.toLowerCase();
    return videoExtensions.contains(extension);
  }

  Widget _buildMediaItem(String url, int index) {
    if (_isVideo(url)) {
      final controller = _videoControllers[index];
      if (controller != null && controller.value.isInitialized) {
        return Stack(
          alignment: Alignment.center,
          children: [
            AspectRatio(
              aspectRatio: controller.value.aspectRatio,
              child: VideoPlayer(controller),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  if (controller.value.isPlaying) {
                    controller.pause();
                  } else {
                    // Restart the video if it reached the end
                    if (controller.value.position ==
                        controller.value.duration) {
                      controller.seekTo(Duration.zero);
                    }
                    controller.play();
                  }
                });
              },
              child: CircleAvatar(
                backgroundColor: Colors.black54,
                child: Icon(
                  controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      } else {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }
    } else {
      return Image.network(
        url,
        fit: BoxFit.contain,
      );
    }
  }

  void _onMessagesDelete({int? id}) async {
    final confirmed = await CoreUtils.showConfirmationDialog(
      context,
      title: 'Delete Message',
      content: 'Are you sure you want to delete this message?',
      action: 'Delete',
    );
    if (confirmed && mounted) {
      final payload = DeleteMessagesPayload(
        userId: widget.userId,
        otherUserId: widget.otherUserId,
        buyerId: widget.buyerId,
        messageIds: {id!},
      );
      context.read<ChatBloc>().add(DeleteMessages(payload));
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: const Text("Media Details"),
        actions: [
          IconButton(
              onPressed: () {
                _onMessagesDelete(id: parentMessageId!);
              },
              icon: const Icon(Icons.delete))
        ],
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(
          left: 16,
          right: 16,
          top: 16,
          bottom: MediaQuery.of(context).viewInsets.bottom + 16,
        ),
        child: SizedBox(
          height: 39,
          child: TextField(
            controller: messageController,
            onSubmitted: (value) => _isMessageEmpty ? () {} : _sendMessage(),
            textInputAction: TextInputAction.send,
            textCapitalization: TextCapitalization.sentences,
            decoration: InputDecoration(
              counterText: '',
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 0,
              ),
              isDense: true,
              hintText: 'iReply',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(100),
                borderSide: BorderSide(
                  color: colors.outline,
                ),
              ),
              suffixIcon: IconButton(
                onPressed: _isMessageEmpty ? () {} : _sendMessage,
                icon: Icon(
                  Icons.send,
                  size: 20,
                  color: _isMessageEmpty ? colors.onSurface : colors.primary,
                ),
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              itemCount: widget.imagePaths.length,
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  selectedIndex = index;
                  parentMessageId = widget.messages[index].id!;
                });
                for (int i = 0; i < widget.imagePaths.length; i++) {
                  if (i != index &&
                      _videoControllers[i]?.value.isPlaying == true) {
                    _videoControllers[i]?.pause();
                  }
                }
              },
              itemBuilder: (context, index) {
                return _buildMediaItem(widget.imagePaths[index], index);
              },
            ),
          ),
          if (widget.imagePaths.length > 1)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        widget.imagePaths.length,
                        (index) => AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          width: selectedIndex == index ? 12 : 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: selectedIndex == index
                                ? AppColors.primary
                                : Colors.grey.shade400,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  void _sendMessage() async {
    String textMessage = messageController.text.trim();
    Loader.show();
    if (parentMessageId != null &&
        parentMessageId != 0 &&
        textMessage.isNotEmpty) {
      final payload = SendReplyMessagePayload(
        buyerId: widget.buyerId,
        message: textMessage,
        userId: widget.userId,
        otherUserId: widget.otherUserId,
        parentId: parentMessageId!,
      );
      context.read<ChatBloc>().add(SendMessage(payload));
      parentMessageId = null;
    }

    Loader.hide();
    messageController.clear();
    Navigator.pop(context);
  }
}
