import 'package:flutter/material.dart';

class ProfileAvatar extends StatefulWidget {
  final String? imageUrl;
  final String avatarPlaceHolderTitle;
  final double radius;
  final Color backgroundColor;
  final TextStyle textStyle;

  const ProfileAvatar({
    Key? key,
    this.imageUrl,
    required this.avatarPlaceHolderTitle,
    this.radius = 28.0,
    this.backgroundColor = Colors.grey,
    required this.textStyle,
  }) : super(key: key);

  @override
  State<ProfileAvatar> createState() => _ProfileAvatarState();
}

class _ProfileAvatarState extends State<ProfileAvatar> {
  bool _invalidImageUrl = false;

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
  radius: widget.radius,
  backgroundColor: widget.backgroundColor,
  backgroundImage: widget.imageUrl != null &&
          !_invalidImageUrl 
      ? NetworkImage(widget.imageUrl!)
      : null,
  onBackgroundImageError: widget.imageUrl != null && !_invalidImageUrl
      ? (exception, stackTrace) {
          setState(() {
            _invalidImageUrl = true;
          });
          print("Image loading error: $exception");
        }
      : null,
  child: widget.imageUrl == null || widget.imageUrl!.isEmpty || _invalidImageUrl
      ? Text(
          widget.avatarPlaceHolderTitle,
          style: widget.textStyle,
        )
      : null,
);
  }
}
