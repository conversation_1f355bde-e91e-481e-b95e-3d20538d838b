# buyer_board

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.
A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Release build command for STAG

flutter build apk --release --target lib/main_staging.dart --flavor staging

## Release build command for PROD

flutter build apk --release --target lib/main_prod.dart --flavor prod

## Build Runner command for code-generation

dart run build_runner build --delete-conflicting-outputs

## How to Use Theme Colors for the Project

### Applying Colors

To apply colors in your project, you can use:
- `context.colorScheme`
- `Theme.of(context).colorScheme`

### Understanding Color Roles

Refer to the [Material 3 design color roles](https://m3.material.io/styles/color/roles) to understand color roles and their usage.

### Color Guidelines

- **Primary Color**: 
  - For light theme: blue
  - For dark theme: light blue
  - Usage: `primary`
  - Contrasting color: `onPrimary`

- **Background Colors**: 
  - For light theme: white
  - For dark theme: dark black
  - Usage: `surface`
  - Contrasting color: `onSurface`

- **Primary Container**: 
  - Consistent light primary color for both light and dark themes
  - Usage: `primaryContainer`
 

  - Contrasting color: `onPrimaryContainer`

- **Error Colors**:
  - Usage: `error`
  - Contrasting color: `onError`

- **Inverse Primary**:
  - For black on light theme and dark on dark theme
  - Usage: `inversePrimary`
  - Contrasting color: `onInversePrimary`

- **Fixed Primary Color**:
  - Always blue for both light and dark themes
  - Usage: `primaryFixed`
  - Contrasting color: `onPrimaryFixed`

- **Medium Grey**:
  - Usage: `onSurfaceVariant`

- **Borders**:
  - Usage: `outline`

- **Dividers**:
  - Usage: `outlineVariant`

### Additional Colors

More color roles may be added over time. Please review the [Material Design color roles](https://m3.material.io/styles/color/roles) to better understand their application.


# Flutter Version
- 3.22.1