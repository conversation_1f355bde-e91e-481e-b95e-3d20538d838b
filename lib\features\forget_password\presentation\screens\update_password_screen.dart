import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/common/widgets/common_text_form_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/core/utils/validators.dart';
import 'package:buyer_board/features/forget_password/presentation/cubit/reset_passsord_cubit.dart';
import 'package:buyer_board/features/splash/presentation/widgets/logo_and_headline_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../auth/presentation/screens/auth_screen.dart';
import '../state/reset_password_state.dart';

class UpdatePasswordScreen extends StatefulWidget {
  const UpdatePasswordScreen({super.key, required this.email});
  final String email;
  @override
  State<UpdatePasswordScreen> createState() => _UpdatePasswordScreenState();
}

class _UpdatePasswordScreenState extends State<UpdatePasswordScreen> {
  final newPasswordController = TextEditingController();
  final confirmNewPasswordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  void onUpdatePassword() {
    if (newPasswordController.text == confirmNewPasswordController.text) {
      final email = widget.email;
      final String newPassword = newPasswordController.text;
      context
          .read<ResetPasswordCubit>()
          .resetPassword(email: email.trim(), password: newPassword);
    } else {
      context.showToast(isSuccess: false, message: Strings.passwordDontMatch);
    }
  }

  void onCancel() {
    authActionNotifier.value = AuthAction.login;
    context.go(PagePath.authScreen);
  }

  @override
  void dispose() {
    newPasswordController.dispose();
    confirmNewPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bottom = context.bottomInsets;
    return BlocListener<ResetPasswordCubit, ResetPasswordState>(
      listener: (context, state) => state.maybeMap(
          loading: (state) => Loader.show(),
          passwordUpdated: (state) => {
                Loader.hide(),
                authActionNotifier.value = AuthAction.login,
                context
                  ..showToast(isSuccess: true, message: state.message)
                  ..go(PagePath.authScreen),
              },
          updatePasswordError: (state) => {
                Loader.hide(),
                context.showToast(isSuccess: false, message: state.error),
              },
          orElse: () => Loader.hide()),
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: AppColors.primary,
        body: Padding(
          padding: const EdgeInsets.all(24),
          child: SafeArea(
            child: SizedBox(
              width: double.maxFinite,
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Visibility(
                        visible: bottom < 24,
                        child: const LogoAndHeadlineSection()),
                    const Spacer(),
                    Row(
                      children: [
                        Text(
                          Strings.enterANewPassword,
                          style: AppStyles.mediumSemiBold
                              .copyWith(color: AppColors.white),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    CommonTextFormField(
                      label: Strings.password,
                      // hint: Strings.newPasswordHint,
                      controller: newPasswordController,
                      borderRadius: BorderRadius.circular(4),
                      labelStyle: AppStyles.smallSemiBold.copyWith(
                        color: AppColors.primaryLight,
                      ),
                      keyboardType: TextInputType.visiblePassword,
                      errorTextColor: context.colorScheme.onPrimaryFixed,
                      outlineErrorBorder: true,
                      showErrorPrefixIcon: true,
                      contextTextColor: context.colorScheme.onPrimaryFixed,
                      validator: Validators().passwordValidator,
                    ),
                    const SizedBox(height: 16),
                    CommonTextFormField(
                      label: Strings.reEnterPassword,

                      // hint: Strings.confirmNewPasswordHint,
                      controller: confirmNewPasswordController,
                      borderRadius: BorderRadius.circular(4),
                      labelStyle: AppStyles.smallSemiBold.copyWith(
                        color: AppColors.primaryLight,
                      ),
                      keyboardType: TextInputType.visiblePassword,
                      errorTextColor: context.colorScheme.onPrimaryFixed,
                      outlineErrorBorder: true,
                      showErrorPrefixIcon: true,
                      contextTextColor: context.colorScheme.onPrimaryFixed,
                      validator: Validators().passwordValidator,
                    ),
                    const SizedBox(height: 32),
                    CommonButton.basic(
                      label: Strings.resetPassword,
                      action: () {
                        if (_formKey.currentState?.validate() ?? false) {
                          onUpdatePassword();
                        }
                      },
                      backgroundColor: AppColors.white,
                      textColor: AppColors.primary,
                    ),
                    CommonButton.basic(
                      label: Strings.cancel,
                      action: onCancel,
                      backgroundColor: AppColors.primary,
                      textColor: AppColors.white,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
