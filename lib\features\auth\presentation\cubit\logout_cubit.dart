import 'package:buyer_board/core/notification_manager/notification_service.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/auth/domain/repository/auth_repository.dart';
import 'package:buyer_board/features/auth/presentation/cubit/auth_navigation_cubit.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_events.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_notification_cubit.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class LogoutCubit extends Cubit<LogoutState> {
  final AuthRepository authRepository;
  final AppPreferences appPreferences;

  LogoutCubit({
    required this.authRepository,
    required this.appPreferences,
  }) : super(LogoutInitial());

  Future<void> logout(BuildContext context) async {
    emit(LogoutLoading());
    try {
      authRepository.logOut();
      emit(LogoutSuccess());
      context.read<UserAuthRouteCubit>().invalidate();
      context.read<AllChatsBloc>().add(const CloseAllChatConnections());
      context.read<ChatNotificationCubit>().closeConnection();
      context.read<BuyersBloc>().add(ClearList());
    } catch (e) {
      emit(LogoutError(e.toString()));
    } finally {
      appPreferences.clearUserData();
      context.read<AllChatsBloc>().add(const CloseAllChatConnections());
      context.read<ChatNotificationCubit>().closeConnection();
      context.go(PagePath.authScreen);
      NotificationService.logout();
    }
  }
}

sealed class LogoutState {}

final class LogoutInitial extends LogoutState {}

final class LogoutLoading extends LogoutState {}

final class LogoutSuccess extends LogoutState {}

final class LogoutError extends LogoutState {
  final String message;

  LogoutError(this.message);
}
