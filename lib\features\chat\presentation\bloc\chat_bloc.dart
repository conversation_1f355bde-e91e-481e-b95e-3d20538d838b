import 'dart:developer';
import 'package:buyer_board/common/utils/image_utils.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/data/models/upload_attachments_model.dart';
import 'package:buyer_board/features/chat/domain/entities/chat_event_enum.dart';
import 'package:buyer_board/features/chat/domain/repositories/chat_repository.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_events.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_states.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ChatRepository chatRepository;
  List<ChatMessage> _messages = [];

  ChatMessage? _lastMessageSent;
  Set<int> _deleteMessageIds = {};

  ChatBloc({required this.chatRepository}) : super(ChatInitialState()) {
    on<GetChat>(_onLoadChat);
    on<SendMessage>(_onSendMessage);
    on<DeleteMessages>(_onDeleteMessages);
    on<RetrySendMessage>(_onRetryMessage);
    on<DiscardFailedMessage>(_discardFailedMessage);
    on<CloseChatConnection>((event, emit) {
      chatRepository.closeConnection();
    });
  }

  void _onLoadChat(GetChat event, Emitter<ChatState> emit) async {
    chatRepository.initializeConnection();
    emit(ChatLoadingState());
    try {
      await chatRepository.connectionReady;
      final response = chatRepository.getChat(event.payload);

      await for (final record in response) {
        log(record.data.toString());
        final ids = record.data.map((e) => e.id);
        Logger().i(ids);
        Logger().w(_messages);
        log(_messages.toString());
        if (record.event == ChatEventType.deleteMessage) {
          _messages = _messages.map(
            (e) {
              if (ids.contains(e.id)) {
                return e.copyWith(isDeleted: true);
              } else {
                return e;
              }
            },
          ).toList();
          emit(ChatDataState(_messages));
        } else if (_deleteMessageIds.isNotEmpty) {
          // Update messages index only that message deleted
          for (var msg in _messages) {
            if (_deleteMessageIds.contains(msg.id)) {
              // Create a new message instance with the updated isDeleted flag
              final updatedMessage = msg.copyWith(isDeleted: true);

              // Update the list with the new message instance
              final index = _messages.indexOf(msg);
              if (index != -1) {
                _messages[index] = updatedMessage;
              }
            }
          }

          _deleteMessageIds = {};
          emit(ChatDataState(_messages));
        } else if (record.event == ChatEventType.phxReply) {
          final messages = record.data;
          _messages.clear();
          _messages.addAll(messages);
          emit(ChatDataState(_messages));
        } else if (record.event == ChatEventType.error) {
          // Handle the 'other' event type
          Logger().w("Received other event type: ${record.event}");
          // You can choose to emit an error or a warning state, or simply log it
          // For example, emit a warning state or keep current messages
          emit(ChatErrorState(
              "problem to send payload")); // Or handle it as needed
        } else {
          // Replace same id message with updated message to avoid duplication, good for UX
          final updatedMessageMap = {for (var msg in record.data) msg.id: msg};
          // Create a Set of existing user IDs for quick lookup
          final existingMessageIds = _messages.map((msg) => msg.id).toSet();

          for (int i = 0; i < _messages.length; i++) {
            final message = _messages[i];
            if (updatedMessageMap.containsKey(message.id)) {
              final updatedMessage = updatedMessageMap[message.id]!;
              _messages[i] = updatedMessage;
            }
          }

          // Add new messages that are not in the existing list
          for (var msg in record.data) {
            if (!existingMessageIds.contains(msg.id)) {
              _messages.add(msg);
            }
          }

          if (_lastMessageSent != null) {
            // Once the message has been sent successfully
            // remove the local message
            _messages.remove(_lastMessageSent!);
            _lastMessageSent = null;
          }
          emit(ChatDataState(_messages));
        }
      }
    } catch (e) {
      log(e.toString());
      emit(ChatErrorState(e.toString()));
    }
  }

  void _onSendMessage(SendMessage event, Emitter<ChatState> emit) async {
    // final payload = event.payload;
    // ChatMessage? newMessage;
    // if (payload is SendMessagePayload) {
    //   newMessage = ChatMessage(
    //     id: DateTime.now().millisecondsSinceEpoch,
    //     message: payload.message,
    //     sentBy: payload.userId,
    //     timestamp: DateTime.now().toString(),
    //     status: ChatMessageStatus.sending,
    //   );
    // } else if (payload is SendReplyMessagePayload) {
    //   newMessage = ChatMessage(
    //     id: DateTime.now().millisecondsSinceEpoch,
    //     message: payload.message,
    //     sentBy: payload.userId,
    //     timestamp: DateTime.now().toString(),
    //     status: ChatMessageStatus.sending,
    //     parentId: payload.parentId,
    //   );
    // }
    // log("---------> $newMessage");
    // if (newMessage != null) {
    //   try {
    //     _lastMessageSent = newMessage;
    //     _messages.add(newMessage);
    //     emit(ChatDataState(_messages));
    //     await chatRepository.sendMessage(payload);
    //   } catch (e) {
    //     _messages.remove(newMessage);
    //     _messages.add(
    //       newMessage.copyWith(status: ChatMessageStatus.failed),
    //     );
    //     emit(ChatDataState(_messages));
    //   }
    // }

    final payload = event.payload;
    ChatMessage? newMessage;
    if (payload is SendMessagePayload) {
      bool hasAttachment =
          payload.attachments != null && payload.attachments!.isNotEmpty;
      bool hasMessage = payload.message.trim().isNotEmpty;
      if (hasMessage && hasAttachment) {
        newMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch,
          message: payload.message,
          sentBy: payload.userId,
          timestamp: DateTime.now().toString(),
          status: ChatMessageStatus.sending,
        );
      } else if (hasMessage) {
        newMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch,
          message: payload.message,
          sentBy: payload.userId,
          timestamp: DateTime.now().toString(),
          status: ChatMessageStatus.sending,
        );
      } else if (hasAttachment) {
        newMessage = ChatMessage(
          id: DateTime.now().millisecondsSinceEpoch,
          message: "",
          sentBy: payload.userId,
          timestamp: DateTime.now().toString(),
          status: ChatMessageStatus.sending,
        );
      }
    } else if (payload is SendReplyMessagePayload) {
      newMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch,
        message: payload.message,
        sentBy: payload.userId,
        timestamp: DateTime.now().toString(),
        status: ChatMessageStatus.sending,
        parentId: payload.parentId,
      );
    }

    log("---------> $newMessage");

    if (newMessage != null) {
      try {
        if (newMessage.message.trim().isNotEmpty ||
            newMessage.attachments.isNotEmpty) {
          _lastMessageSent = newMessage;
          _messages.add(newMessage);
          emit(ChatDataState(_messages));
        }
        await chatRepository.sendMessage(payload);
      } catch (e) {
        _messages.remove(newMessage);
        _messages.add(
          newMessage.copyWith(status: ChatMessageStatus.failed),
        );
        emit(ChatDataState(_messages));
      }
    }

    if (payload is EditMessagePayload) {
      final index = _messages.indexWhere((msg) => msg.id == payload.messageId);
      final updatedMessage = _messages[index].copyWith(
        message: payload.message,
        timestamp: DateTime.now().toString(),
        sentBy: payload.userId,
        status: ChatMessageStatus.sending,
      );
      _messages[index] = updatedMessage;

      try {
        emit(ChatDataState(_messages));
        await chatRepository.sendMessage(payload);
      } catch (e) {
        emit(ChatErrorState(e.toString()));
      }
    }
  }

  void _onRetryMessage(RetrySendMessage event, Emitter<ChatState> emit) {
    _messages.removeWhere((msg) => msg.id == event.id);
    emit(ChatDataState(_messages));
    add(SendMessage(event.payload));
  }

  void _discardFailedMessage(
      DiscardFailedMessage event, Emitter<ChatState> emit) {
    // final index = _messages.indexWhere((msg) => msg.id == event.id);
    // _messages.removeAt(index);
    _messages.removeWhere((msg) => msg.id == event.id);
    emit(ChatDataState(_messages));
  }

  void _onDeleteMessages(DeleteMessages event, Emitter<ChatState> emit) async {
    final payload = event.payload as DeleteMessagesPayload;
    _deleteMessageIds = payload.messageIds;

    for (var msg in _messages) {
      if (_deleteMessageIds.contains(msg.id)) {
        // Create a new message instance with the updated isDeleted flag
        final updatedMessage = msg.copyWith(isDeleted: true);

        // Update the list with the new message instance
        final index = _messages.indexOf(msg);
        if (index != -1) {
          _messages[index] = updatedMessage;
        }
      }
    }

    emit(ChatDataState(_messages));
    chatRepository.deleteMessages(event.payload);
  }

  List<String>? selectedImagePath;
  Future<Map<String, dynamic>> selectAttachments() async {
    final result = await ImageUtils().pickMultipleImagesFromGallery();
    final validFiles = result['valid'] as List<String>? ?? [];
    final hasInvalidFiles = result['hasInvalidFiles'] as bool? ?? false;
    selectedImagePath = validFiles;
    return {
      "valid": validFiles,
      "hasInvalidFiles": hasInvalidFiles,
      "error": result['error'],
    };
  }

  List<String>? selectedDocPath;

  Future<List<String>?> selectDocuments() async {
    selectedDocPath = await ImageUtils().pickMultipleDoc();
    return selectedDocPath;
  }

  List<UploadAttachment>? attachmentResponse;
  Future<UploadAttachment?> uploadAttachments(String imagesPath) async {
    try {
      final attachmentResponse =
          await ImageUtils().uploadMultipleImages(imagesPath);
      if (attachmentResponse != null) {
        return attachmentResponse;
      }
      return null;
    } catch (e) {
      print('Error: $e');
      return null;
    }
  }

  Future<String?> deleteAttachments(
      List<Map<String, dynamic>> imagesPath) async {
    try {
      final attachmentResponse =
          await chatRepository.deleteAttachments(images: imagesPath);
      if (attachmentResponse != "") {
        return attachmentResponse;
      }
      return null;
    } catch (e) {
      print('Error: $e');
      return null;
    }
  }
}
