import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:flutter/material.dart';

class MenuItem extends StatelessWidget {
  const MenuItem({
    super.key,
    required this.title,
    this.leadingIcon,
    this.trailingIcon,
    this.onTapMenuItem,
    this.subtitle,
    this.titleTextStyle,
    this.listTileShape,
    this.leadingTitle,
    this.leadingTitleGap,
  });
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final String title;
  final Widget? subtitle;
  final VoidCallback? onTapMenuItem;
  final ShapeBorder? listTileShape;
  final TextStyle? titleTextStyle;
  final Widget? leadingTitle;
  final double? leadingTitleGap;

  @override
  Widget build(BuildContext context) {
    // final colorScheme = context.colorScheme;
    return ListTile(
        shape: listTileShape,
        onTap: onTapMenuItem,
        minLeadingWidth: Dimensions.materialPadding,
        titleAlignment: ListTileTitleAlignment.center,
        titleTextStyle: titleTextStyle ?? context.typography.largeSemi,
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (leadingTitle != null) ...[
              leadingTitle!,
              SizedBox(width: leadingTitleGap ?? 6),
            ],
            Text(title),
          ],
        ),
        subtitle: subtitle,
        leading: leadingIcon,
        trailing: trailingIcon);
  }
}
