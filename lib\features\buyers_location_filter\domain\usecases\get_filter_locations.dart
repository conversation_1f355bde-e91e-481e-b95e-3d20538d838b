import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/repositories/buyer_location_filter_repository.dart';


class GetFilterLocationsUseCase {
  final BuyerLocationFilterRepository repository;

  GetFilterLocationsUseCase(this.repository);

  Future<List<LocationEntity>> call({required int page, required int pageSize}) async {
    return await repository.getLocations(page: page, pageSize: pageSize);
  }
  



}

