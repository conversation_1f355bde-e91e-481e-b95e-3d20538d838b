// ignore_for_file: use_build_context_synchronously

import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/core/utils/text_field_formatters.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/add_buyer_cubit.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/additional_desires_section.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/agreement_expiration_date_section.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/buyer_details_section.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/buyer_location_section.dart';
import 'package:buyer_board/features/add_buyer/presentation/widgets/buyer_needs_section.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_bloc.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../core/resources/resources.dart';
import '../../../home/<USER>/cubit/home_tab_cubit.dart';
import '../../data/models/buyer_model.dart';
import '../cubit/buyer_info_cubit.dart';
import '../states/add_buyer_state.dart';

class AddBuyerScreen extends StatefulWidget {
  const AddBuyerScreen({
    super.key,
    this.buyer,
    this.isEditMode = false,
  });
  final BuyerModel? buyer;
  final bool isEditMode;

  @override
  State<AddBuyerScreen> createState() => _AddBuyerScreenState();
}

class _AddBuyerScreenState extends State<AddBuyerScreen> {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailAddressController = TextEditingController();
  final TextEditingController primaryPhoneNumberController =
      TextEditingController();
  final CurrencyTextEditingController minimumBudgetController =
      CurrencyTextEditingController();
  final TextEditingController minimumBedRoomsController =
      TextEditingController();
  final TextEditingController minimumBathRoomsController =
      TextEditingController();
  final CurrencyTextEditingController minimumSquareFootageController =
      CurrencyTextEditingController();
  final List<ZipCodeEditingController> zipCodeControllers = [];
  late final TextEditingController expirationDateTimeController;
  final _formKey = GlobalKey<FormState>();
  String? buyerProfileImage;
  @override
  void initState() {
    initFieldsForEdit();
    setTimeOffset();
    super.initState();
  }

  void initFieldsForEdit() {
    expirationDateTimeController = TextEditingController(
        text: widget.isEditMode
            ? null
            : DateFormat(Constants.dateFormatter)
                .format(DateTime.now().add(const Duration(days: 90))));
    if (widget.buyer != null) {
      final buyer = widget.buyer!;
      buyerProfileImage = buyer.imageUrl;
      firstNameController.text = buyer.firstName ?? '';
      lastNameController.text = buyer.lastName ?? '';
      emailAddressController.text = buyer.email ?? '';
      primaryPhoneNumberController.text = buyer.primaryPhoneNumber ?? '';
      minimumBudgetController.text = CoreUtils.formatCurrency(
          double.tryParse(buyer.buyerNeeds?.budget ?? '0') ?? 0);
      minimumBedRoomsController.text =
          buyer.buyerNeeds?.minBedrooms.toString() ?? '';
      minimumBathRoomsController.text =
          buyer.buyerNeeds?.minBathrooms.toString() ?? '';
      minimumSquareFootageController.text =
          CoreUtils.formatCurrency(symbol: '', buyer.buyerNeeds?.minArea ?? 0);
      expirationDateTimeController.text = buyer.buyerExpirationDate != null
          ? DateFormat(Constants.dateFormatter)
              .format(buyer.buyerExpirationDate!)
          : '';
      syncPurchaseType(buyer.buyerNeeds?.purchaseType?.jsonValue);
      syncPropertyType(buyer.buyerNeeds?.propertyType?.jsonValue);
      syncFinancialStatus(buyer.buyerNeeds?.financialStatus?.jsonValue);
      syncBuyerLocations(buyer.buyerLocationsOfInterest);
      syncAdditionalDesires(buyer.additionalRequests);
      context.read<BuyerInfoCubit>().syncBuyerInfo(buyer);
    }
  }

  Future<void> setTimeOffset() async {
    final offset = DateTime.now().timeZoneOffset;
    final offsetInHours = offset.inHours;
    context.read<BuyerInfoCubit>().setTimeOffset(offsetInHours);
  }

  void syncBuyerLocations(List<String> locations) {
    context.read<BuyerInfoCubit>().syncBuyerLocationsOfInterests(locations);
  }

  void syncAdditionalDesires(List<String> desires) {
    context.read<BuyerInfoCubit>().syncAdditionalRequests(desires);
  }

  void syncPurchaseType(String? purchaseType) {
    for (var value in PurchaseType.values) {
      if (value.name == purchaseType) {
        purchaseTypeNotifier.value = value;
        break;
      }
    }
  }

  void syncPropertyType(String? propertyType) {
    for (var value in PropertyType.values) {
      if (value.jsonValue == propertyType) {
        propertyTypeNotifier.value = value;
        break;
      }
    }
  }

  void syncFinancialStatus(String? financialStatus) {
    for (var value in FinancialStatus.values) {
      if (value.jsonValue == financialStatus) {
        financialStatusNotifier.value = value;
        break;
      }
    }
  }

  void syncTimeline(String? timeline) {
    for (var value in TimeLine.values) {
      if (value.name == timeline) {
        timelineNotifier.value = value;
        break;
      }
    }
  }

  void resetAddBuyerInfo(BuyerModel buyer) {
    firstNameController.clear();
    lastNameController.clear();
    emailAddressController.clear();
    primaryPhoneNumberController.clear();
    minimumBudgetController.clear();
    minimumBedRoomsController.clear();
    minimumBathRoomsController.clear();
    minimumSquareFootageController.clear();
    expirationDateTimeController.clear();
    purchaseTypeNotifier.value = null;
    propertyTypeNotifier.value = null;
    financialStatusNotifier.value = null;
    timelineNotifier.value = TimeLine.none;
    context.read<BuyerInfoCubit>().resetBuyerInfo();
    widget.buyer != null
        ? {
            context
              ..go(PagePath.mainScreen)
              ..push(PagePath.expandedBuyerCard, extra: buyer)
          }
        : context.read<HomeBottomNarBarTabCubit>().changeTab(0);
    context
        .read<BuyersBloc>()
        .add(LoadBuyers(forceRefresh: true, context: context));
  }

  void onSave() {
    final propertyTypeProvided = propertyTypeNotifier.value == null;
    final purchaseTypeProvided = purchaseTypeNotifier.value == null;
    final financialStatusProvided = financialStatusNotifier.value == null;
    propertyTypeError.value = "";
    purchaseTypeError.value = "";
    financialStatusError.value = "";
    bool hasZipCodeError = zipCodeControllers.any((controller) {
      return controller.errorText != null && controller.errorText!.isNotEmpty;
    });
    if (hasZipCodeError) {
      return;
    }
    final formIsValid = _formKey.currentState?.validate() ?? false;
    if (formIsValid &&
        !propertyTypeProvided &&
        !purchaseTypeProvided &&
        !financialStatusProvided) {
      saveBuyerInfo();
    } else {
      if (!formIsValid) {
        if (purchaseTypeProvided) {
          purchaseTypeError.value = "Select Value";
        }
        if (propertyTypeProvided) {
          propertyTypeError.value = "Select Value";
        }
        if (financialStatusProvided) {
          financialStatusError.value = "Select Value";
        }
      } else {
        if (propertyTypeProvided) {
          propertyTypeError.value = "Select Value";
        }
        if (purchaseTypeProvided) {
          purchaseTypeError.value = "Select Value";
        }
        if (financialStatusProvided) {
          financialStatusError.value = "Select Value";
        }
      }
    }
  }

  Future<void> saveBuyerInfo() async {
    final buyerInfo = context.read<BuyerInfoCubit>().state;
    await context
        .read<AddBuyerCubit>()
        .addBuyer(buyerInfo: buyerInfo, id: widget.buyer?.id);
    if (mounted) {
      context.read<BuyersBloc>().add(LoadBuyers(context: context, page: 1));
    }
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    final xColors = context.appColors;
    final typography = context.typography;
    return BlocListener<AddBuyerCubit, AddBuyerState>(
      listener: (context, state) {
        state.mapOrNull(
          loading: (state) => Loader.show(),
          success: (state) {
            Loader.hide();
            context.showToast(isSuccess: true, message: state.message);
            resetAddBuyerInfo(state.buyer);
          },
          addBuyerError: (state) {
            Loader.hide();
            context.showToast(isSuccess: false, message: state.error);
          },
        );
      },
      child: PopScope(
        onPopInvoked: (didPop) {
          if (didPop) {
            if (widget.buyer != null) {
              resetAddBuyerInfo(widget.buyer!);
            }
          }
        },
        child: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Scaffold(
            appBar: AppBar(
              title: Text(
                widget.isEditMode ? 'Update Buyer' : Strings.addBuyer,
              ),
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(Dimensions.materialPadding),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      Strings.buyerDetails,
                      style: typography.large1xBlack.copyWith(
                        color: xColors.blackWhite,
                      ),
                    ),
                    Text(
                      Strings.buyerDetailsDesc,
                      style: typography.mediumReg,
                    ),
                    const SizedBox(height: 24),
                    BuyerDetailsSection(
                      readOnly: false,
                      isEditMode: widget.isEditMode,
                      firstNameController: firstNameController,
                      lastNameController: lastNameController,
                      emailAddressController: emailAddressController,
                      primaryPhoneNumberController:
                          primaryPhoneNumberController,
                    ),
                    spacerH12,
                    BuyerLocationSection(
                      sync: widget.buyer != null,
                      zipCodeControllers: zipCodeControllers,
                    ),
                    const SizedBox(height: 16),
                    BuyerNeedsSection(
                      minimumBudgetController: minimumBudgetController,
                      minimumBedRoomsController: minimumBedRoomsController,
                      minimumBathRoomsController: minimumBathRoomsController,
                      minimumSquareFootageController:
                          minimumSquareFootageController,
                    ),
                    spacerH12,
                    AdditionalRequestsSection(sync: widget.buyer != null),
                    const SizedBox(height: 16),
                    BuyerExpirationDateSection(
                      expirationDateTimeController:
                          expirationDateTimeController,
                    ),
                    const SizedBox(height: 32),
                    CommonButton.basic(
                      label: Strings.save,
                      action: onSave,
                      backgroundColor: context.theme.appColors.pPXLight,
                      textColor: context.theme.appColors.whitePXDark,
                    ),
                    const SizedBox(height: 4),
                    CommonButton(
                      buttonType: ButtonType.outline,
                      label: Strings.cancel,
                      action: () {
                        widget.isEditMode
                            ? context.pop()
                            : context
                                .read<HomeBottomNarBarTabCubit>()
                                .changeTab(0);
                      },
                      backgroundColor: colors.primary,
                    ),
                    CommonButton.basic(
                      label: 'Delete',
                      action: () {},
                      textColor: colors.error,
                      backgroundColor: Colors.transparent,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    emailAddressController.dispose();
    primaryPhoneNumberController.dispose();
    minimumBudgetController.dispose();
    minimumBedRoomsController.dispose();
    minimumBathRoomsController.dispose();
    minimumSquareFootageController.dispose();
    expirationDateTimeController.dispose();
    propertyTypeError.value = "";
    purchaseTypeError.value = "";
    financialStatusError.value = "";
    propertyTypeNotifier.value = null;
    purchaseTypeNotifier.value = null;
    financialStatusNotifier.value = null;
    super.dispose();
  }
}
