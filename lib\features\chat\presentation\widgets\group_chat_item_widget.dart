import 'package:buyer_board/common/widgets/profile_avatar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class GroupChatItemWidget extends StatefulWidget {
  const GroupChatItemWidget({
    super.key,
    required this.messages,
    this.showAvatar = true,
    this.hasTail = true,
    required this.imageUrl,
    required this.avatarPlaceHolderTitle,
    required this.isSender,
    required this.id,
    this.onLongPress,
    this.onTap,
    this.isDeleteMode = false,
    this.isSelectedForDeletion = false,
    this.onSelectedForDeletion,
    this.onRetrySendMessage,
    this.onDiscardFailedMessage,
    required this.onLongPressStart,
      required this.userId,  required this.otherUserId,  required this.buyerId,
  });
    final int userId;
  final int otherUserId;
  final int buyerId;
  final List<ChatMessage> messages;
  final String? imageUrl;
  final String avatarPlaceHolderTitle;
  final bool showAvatar;
  final bool hasTail;
  final bool isSender;
  final int id;
  final VoidCallback? onLongPress;
  final bool isDeleteMode;
  final bool isSelectedForDeletion;
  final VoidCallback? onTap;
  final void Function(int)? onSelectedForDeletion;
  final Function(dynamic details) onLongPressStart;
  final VoidCallback? onRetrySendMessage;
  final VoidCallback? onDiscardFailedMessage;

  @override
  State<GroupChatItemWidget> createState() => _GroupChatItemWidgetState();
}

class _GroupChatItemWidgetState extends State<GroupChatItemWidget> {
  bool invalidImageUrl = false;
  @override
  Widget build(BuildContext context) {
    final messageTime = DateTime.tryParse(widget.messages.last.timestamp);
    final localTime =
        messageTime != null ? messageTime.toLocal() : DateTime.now().toLocal();
    final time = DateFormat('hh:mm a').format(localTime);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              widget.showAvatar
                  ? ProfileAvatar(
                      imageUrl: widget.imageUrl,
                      avatarPlaceHolderTitle: widget.avatarPlaceHolderTitle,
                      textStyle: context.typography.largeSemi.copyWith(
                        color: context.theme.appColors.whitePXDark,
                      ),
                      radius: 28,
                      backgroundColor: context.theme.appColors.pPXLight,
                    )
                  : const SizedBox(width: 56),
              const SizedBox(
                width: 10,
              ),
              Flexible(
                child: GestureDetector(
                    onTap: widget.onTap,
                    child: ImageChatBubble(
                      chatEntities: widget.messages,
                      time: time,
                      isReceiver: !widget.isSender,
                      hasTail: widget.hasTail,
                      otherUserId: widget.otherUserId,
                      userId: widget.userId,
                      buyerId: widget.buyerId,
                    )),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class ImageChatBubble extends StatelessWidget {
  final List<ChatMessage> chatEntities;
  final bool isReceiver;
  final bool hasTail;
  final String time;
  final int userId;
  final int otherUserId;
  final int buyerId;

  const ImageChatBubble({
    super.key,
    required this.chatEntities,
    required this.time,
    required this.isReceiver,
    required this.otherUserId,
    required this.userId,
    required this.buyerId,
    this.hasTail = true,
  });

  @override
  Widget build(BuildContext context) {
    List<String> images = [];
    List thumbnails = [];
    for (var chatEntity in chatEntities) {
      images.addAll(chatEntity.attachments
          .where((attachment) => attachment.type == 'image')
          .map((attachment) => attachment.url)
          .toList());
      thumbnails.addAll(chatEntity.attachments
          .where((attachment) => attachment.type == 'video')
          .map((attachment) => attachment.thumbnailUrl ?? "")
          .toList());
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: InkWell(
        onTap: () {
          context.pushNamed(
            PagePath.attachmentDetails,
            extra: {
              'imagePaths': images,
              'userId': userId,
              'otherUserId': otherUserId,
              'buyerId': buyerId,
              'messages': chatEntities,
            },
          );
        },
        child: CustomPaint(
          painter: _ImageChatBubbleClipper(
            color: (isReceiver
                    ? context.appColors.greyXLightPDark
                    : context.appColors.pPXLight)
                .withOpacity(
              1,
            ),
            hasTail: hasTail,
          ),
          child: Container(
            margin: const EdgeInsets.fromLTRB(10, 2, 2, 2),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(child: _buildMediaGrid(context, images, thumbnails)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediaGrid(
      BuildContext context, List attachments, List thumbnailUrl) {
    final int itemCount = attachments.length > 4 ? 4 : attachments.length;
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 4,
          crossAxisSpacing: 4,
          childAspectRatio: 1),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        final attachment = attachments[index];
        final thumbnail =
            index < thumbnailUrl.length ? thumbnailUrl[index] : "";
        if (index == 3 && attachments.length > 4) {
          return Stack(
            fit: StackFit.expand,
            children: [
              _buildThumbnailWidget(attachment, thumbnailUrl: thumbnail),
              Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                alignment: Alignment.center,
                child: Text(
                  '+${attachments.length - 3}',
                  style: const TextStyle(color: Colors.white, fontSize: 18),
                ),
              ),
            ],
          );
        }
        return _buildThumbnailWidget(attachment, thumbnailUrl: thumbnail);
      },
    );
  }

  Widget _buildThumbnailWidget(
    String url, {
    String? thumbnailUrl,
  }) {
    if (isVideo(url)) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: SizedBox(
          width: double.infinity,
          height: 180,
          child: Stack(
            children: [
              Center(
                child: Image.network(
                  thumbnailUrl!,
                  fit: BoxFit.cover,
                ),
              ),
              Align(
                alignment: Alignment.center,
                child: Icon(
                  Icons.play_circle_filled,
                  color: Colors.white.withOpacity(0.8),
                  size: 48,
                ),
              ),
            ],
          ),
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(url, fit: BoxFit.cover),
    );
  }

  bool isVideo(String url) {
    final Uri parsedUri = Uri.parse(url);
    final String path = parsedUri.path;
    final String extension = path.split('.').last.toLowerCase();
    const videoExtensions = ['mp4', 'mov', 'avi', 'mkv'];
    return videoExtensions.contains(extension);
  }
}

class _ImageChatBubbleClipper extends CustomPainter {
  final Color color;
  final bool hasTail;

  _ImageChatBubbleClipper({
    required this.color,
    this.hasTail = true,
  });

  final double _radius = 8;

  @override
  void paint(Canvas canvas, Size size) {
    var h = size.height;
    var w = size.width;

    var path = Path();

    if (hasTail) {
      // Bubble path with tail
      path.moveTo(_radius * 3, 0);
      path.quadraticBezierTo(_radius, 0, _radius, _radius * 1.5);
      path.lineTo(_radius, h - _radius * 1.5);
      path.quadraticBezierTo(_radius * .8, h, 0, h);
      path.quadraticBezierTo(_radius * 1, h, _radius * 1.5, h - _radius * 0.6);
      path.quadraticBezierTo(_radius * 1.5, h, _radius * 3, h);
      path.lineTo(w - _radius * 2, h);
      path.quadraticBezierTo(w, h, w, h - _radius * 1.5);
      path.lineTo(w, _radius * 1.5);
      path.quadraticBezierTo(w, 0, w - _radius * 2, 0);
    } else {
      // Bubble path without tail
      path.moveTo(_radius * 3, 0);
      path.quadraticBezierTo(_radius, 0, _radius, _radius * 1.5);
      path.lineTo(_radius, h - _radius * 1.5);
      path.quadraticBezierTo(_radius, h, _radius * 3, h);
      path.lineTo(w - _radius * 2, h);
      path.quadraticBezierTo(w, h, w, h - _radius * 1.5);
      path.lineTo(w, _radius * 1.5);
      path.quadraticBezierTo(w, 0, w - _radius * 2, 0);
    }

    canvas.clipPath(path);
    canvas.drawRRect(
        RRect.fromLTRBR(0, 0, w, h, Radius.zero),
        Paint()
          ..color = color
          ..style = PaintingStyle.fill);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
