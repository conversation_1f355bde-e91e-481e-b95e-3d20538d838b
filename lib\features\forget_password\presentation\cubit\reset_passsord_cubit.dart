import 'package:buyer_board/features/auth/data/models/requests/auth_request.dart';
import 'package:buyer_board/features/forget_password/data/models/reset_password_request.dart';
import 'package:buyer_board/features/forget_password/data/models/verify_otp_request.dart';
import 'package:buyer_board/features/forget_password/domain/repository/forget_password_repository.dart';
import 'package:buyer_board/features/forget_password/presentation/state/reset_password_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  ResetPasswordCubit({required this.resetPasswordRepository})
      : super(const ResetPasswordState.initial());
  final ResetPasswordRepository resetPasswordRepository;

  void requestOtp({required String email}) async {
    emit(const ResetPasswordState.loading());
    try {
      final response = await resetPasswordRepository.forgetPassword(
          resetPasswordRequest: ResetPasswordRequest(email: email));
      print("RESET PASSWORD RESPONSE DATA: ${response.data}");
      emit(ResetPasswordState.otpSent(response.message.body ?? ''));
    } catch (e) {
      emit(ResetPasswordState.resetPasswordError(e.toString()));
    }
  }

  void verifyOtp({required String email, required String otp}) async {
    emit(const ResetPasswordState.loading());
    try {
      final response = await resetPasswordRepository.verifyOtp(
          verifyOtpRequest: VerifyOtpRequest(email: email, otp: otp));
      print("VERIFY OTP RESPONSE DATA: ${response.data}");
      emit(ResetPasswordState.otpVerified(response.message.body ?? ''));
    } catch (e) {
      emit(ResetPasswordState.resetPasswordError(e.toString()));
    }
  }

  void resetPassword({required String email, required String password}) async {
    emit(const ResetPasswordState.loading());
    try {
      final response = await resetPasswordRepository.updatePassword(
        authRequest: AuthRequest(email: email, password: password),
      );
      emit(ResetPasswordState.passwordUpdated(response.message.body ?? ''));
    } catch (e) {
      emit(ResetPasswordState.resetPasswordError(e.toString()));
    }
  }
}
