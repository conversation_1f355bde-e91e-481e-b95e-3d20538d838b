import 'package:buyer_board/common/widgets/bubble_chat.dart';
import 'package:buyer_board/common/widgets/profile_avatar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class ChatItemWidget extends StatefulWidget {
  const ChatItemWidget({
    super.key,
    required this.chatEntity,
    this.showAvatar = true,
    this.hasTail = true,
    required this.imageUrl,
    required this.avatarPlaceHolderTitle,
    required this.isSender,
    required this.id,
    this.onLongPress,
    this.onTap,
    this.isDeleteMode = false,
    this.isSelectedForDeletion = false,
    this.onSelectedForDeletion,
    this.onSelectedForMultipleDeletion,
    this.onRetrySendMessage,
    this.onDiscardFailedMessage,
    required this.onLongPressStart,
    required this.userId,
    required this.otherUserId,
    required this.buyerId,
  });
  final int userId;
  final int otherUserId;
  final int buyerId;
  final ChatMessage chatEntity;
  final String? imageUrl;
  final String avatarPlaceHolderTitle;
  final bool showAvatar;
  final bool hasTail;
  final bool isSender;
  final int id;
  final VoidCallback? onLongPress;
  final bool isDeleteMode;
  final bool isSelectedForDeletion;
  final VoidCallback? onTap;
  final void Function(int)? onSelectedForDeletion;
  final void Function(int)? onSelectedForMultipleDeletion;

  final Function(dynamic details) onLongPressStart;
  final VoidCallback? onRetrySendMessage;
  final VoidCallback? onDiscardFailedMessage;

  @override
  State<ChatItemWidget> createState() => _ChatItemWidgetState();
}

class _ChatItemWidgetState extends State<ChatItemWidget> {
  bool invalidImageUrl = false;
  @override
  Widget build(BuildContext context) {
    final messageTime = DateTime.tryParse(widget.chatEntity.timestamp);
    final localTime =
        messageTime != null ? messageTime.toLocal() : DateTime.now().toLocal();
    final time = DateFormat('hh:mm a').format(localTime);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (widget.isDeleteMode &&
                  !(widget.chatEntity.isDeleted ?? false))
                _DeleteCheckbox(
                  isSelectedForDeletion: widget.isSelectedForDeletion,
                  onChanged: (value) {
                    widget.onSelectedForMultipleDeletion?.call(widget.id);
                  },
                ),
              widget.showAvatar
                  ? ProfileAvatar(
                      imageUrl: widget.imageUrl,
                      avatarPlaceHolderTitle: widget.avatarPlaceHolderTitle,
                      textStyle: context.typography.largeSemi.copyWith(
                        color: context.theme.appColors.whitePXDark,
                      ),
                      radius: 28,
                      backgroundColor: context.theme.appColors.pPXLight,
                    )
                  : const SizedBox(width: 56),
              const SizedBox(
                width: 10,
              ),
              Flexible(
                child: GestureDetector(
                    onTap: widget.onTap,
                    onLongPress: widget.onLongPress,
                    onLongPressStart: widget.onLongPressStart,
                    child: widget.chatEntity.attachments.isEmpty
                        ? _ChatBubble(
                            chatEntity: widget.chatEntity,
                            time: time,
                            isReceiver: !widget.isSender,
                            hasTail: widget.hasTail,
                          )
                        : ImageChatBubble(
                            chatEntity: widget.chatEntity,
                            time: time,
                            isReceiver: !widget.isSender,
                            hasTail: widget.hasTail,
                            otherUserId: widget.otherUserId,
                            userId: widget.userId,
                            buyerId: widget.buyerId,
                          )),
              ),
              if (widget.chatEntity.status == ChatMessageStatus.failed)
                IconButton(
                  style: IconButton.styleFrom(
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    padding: EdgeInsets.zero,
                    visualDensity: VisualDensity.compact,
                  ),
                  onPressed: () {
                    _showCancelOrRetrySheet(context);
                  },
                  icon: Icon(
                    Icons.error,
                    color: context.appColors.error,
                  ),
                ),
            ],
          ),
        ),
        if (widget.chatEntity.status == ChatMessageStatus.sending)
          Padding(
            padding: const EdgeInsetsDirectional.only(start: 76, top: 4),
            child: Text(
              'Sending...',
              style: context.typography.small1xReg.copyWith(
                color: context.theme.appColors.greyM,
              ),
            ),
          ),
        if (widget.chatEntity.status == ChatMessageStatus.failed)
          Padding(
            padding: const EdgeInsetsDirectional.only(start: 76, top: 4),
            child: Text(
              'Failed to send',
              style: context.typography.small1xReg.copyWith(
                color: context.theme.appColors.error,
              ),
            ),
          ),
      ],
    );
  }

  void _showCancelOrRetrySheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                const SizedBox(width: 34),
                const Spacer(),
                Text(
                  'Message not sent',
                  style: context.typography.mediumSemi,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            ListTile(
              leading: const Icon(Icons.arrow_upward),
              title: const Text('Try again'),
              onTap: () {
                widget.onRetrySendMessage?.call();
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              leading: Icon(Icons.delete, color: context.appColors.error),
              title: Text(
                'Delete',
                style: context.typography.mediumReg
                    .copyWith(color: context.appColors.error),
              ),
              onTap: () {
                widget.onDiscardFailedMessage?.call();
                Navigator.of(context).pop();
              },
            ),
            SizedBox(height: MediaQuery.paddingOf(context).bottom + 16),
          ],
        );
      },
    );
  }
}

class _DeleteCheckbox extends StatefulWidget {
  const _DeleteCheckbox({
    required this.isSelectedForDeletion,
    required this.onChanged,
  });

  final bool isSelectedForDeletion;
  final void Function(bool?)? onChanged;

  @override
  State<_DeleteCheckbox> createState() => _DeleteCheckboxState();
}

class _DeleteCheckboxState extends State<_DeleteCheckbox> {
  @override
  Widget build(BuildContext context) {
    return Checkbox(
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      value: widget.isSelectedForDeletion,
      onChanged: (value) {
        widget.onChanged?.call(value);
      },
    );
  }
}

class _ChatBubble extends StatelessWidget {
  const _ChatBubble({
    required this.chatEntity,
    required this.time,
    required this.hasTail,
    required this.isReceiver,
  });

  final ChatMessage chatEntity;
  final String time;
  final bool hasTail;
  final bool isReceiver;

  @override
  Widget build(BuildContext context) {
    final bool isDeleted = chatEntity.isDeleted ?? false;
    return IntrinsicWidth(
      child: ChatBubble(
        color: (isReceiver
                ? context.appColors.greyXLightPDark
                : context.appColors.pPXLight)
            .withOpacity(
          chatEntity.isDeleted ?? false ? .5 : 1,
        ),
        hasTail: hasTail,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (chatEntity.parentMessage != null &&
                !(chatEntity.isDeleted ?? false))
              Row(
                children: [
                  if (chatEntity.parentMessage!.isDeleted == false &&
                      chatEntity.parentMessage!.attachments.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(right: 18.0, bottom: 5),
                      child: chatEntity.parentMessage!.attachments
                              .any((attachment) => attachment.type == 'video')
                          ? Stack(
                              alignment: Alignment.center,
                              children: [
                                Image.network(
                                  chatEntity.parentMessage!.attachments
                                          .firstWhere((attachment) =>
                                              attachment.type == 'video')
                                          .thumbnailUrl ??
                                      '',
                                  width: 40,
                                  height: 40,
                                  fit: BoxFit.cover,
                                ),
                                const Icon(Icons.play_circle_fill,
                                    color: Colors.white, size: 24),
                              ],
                            )
                          : chatEntity.parentMessage!.attachments.any(
                                  (attachment) => attachment.type == 'image')
                              ? Image.network(
                                  chatEntity.parentMessage!.attachments
                                      .firstWhere((attachment) =>
                                          attachment.type == 'image')
                                      .url,
                                  width: 40,
                                  height: 40,
                                  fit: BoxFit.cover,
                                )
                              : const SizedBox.shrink(),
                    ),
                  Expanded(
                    child: Text(
                      chatEntity.parentMessage!.message.isNotEmpty == true
                          ? chatEntity.parentMessage!.message
                          : (chatEntity.parentMessage!.attachments.isNotEmpty ==
                                  true
                              ? (chatEntity.parentMessage!.attachments.any(
                                      (attachment) =>
                                          attachment.type == 'video')
                                  ? 'Video'
                                  : chatEntity.parentMessage!.attachments.any(
                                          (attachment) =>
                                              attachment.type == 'image')
                                      ? 'Photo'
                                      : '')
                              : ''),
                      style: AppStyles.smallSemiBold.copyWith(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          color: (isReceiver
                              ? context.appColors.blackWhite
                              : context.appColors.whitePXDark)),
                    ),
                  ),
                ],
              ),
            Text(
              isDeleted ? 'Message deleted' : chatEntity.message,
              style: AppStyles.medium.copyWith(
                color: (isReceiver
                        ? context.appColors.blackWhite
                        : context.appColors.whitePXDark)
                    .withOpacity(
                  isDeleted ? .5 : 1,
                ),
              ),
            ),
            if (!isDeleted) ...[
              spacerH4,
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    time,
                    style: AppStyles.small.copyWith(
                      color: isReceiver
                          ? context.appColors.greyMP
                          : context.appColors.pLightGrey,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class ImageChatBubble extends StatelessWidget {
  final ChatMessage chatEntity;
  final bool isReceiver;
  final bool hasTail;
  final String time;
  final int userId;
  final int otherUserId;
  final int buyerId;

  const ImageChatBubble({
    super.key,
    required this.chatEntity,
    required this.time,
    required this.isReceiver,
    this.hasTail = true,
    required this.otherUserId,
    required this.userId,
    required this.buyerId,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDeleted = chatEntity.isDeleted ?? false;
    String message = isDeleted ? 'Message deleted' : chatEntity.message;
    List<String> images =
        chatEntity.attachments.map((attachment) => attachment.url).toList();
    List thumbnailUrl = chatEntity.attachments
        .map((attachment) => attachment.thumbnailUrl ?? "")
        .toList();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: InkWell(
        onTap: () {
          context.pushNamed(
            PagePath.attachmentDetails,
            extra: {
              'imagePaths': images,
              'userId': userId,
              'otherUserId': otherUserId,
              'buyerId': buyerId,
              'messages': [chatEntity]
            },
          );
        },
        child: CustomPaint(
          painter: _ImageChatBubbleClipper(
            color: (isReceiver
                    ? context.appColors.greyXLightPDark
                    : context.appColors.pPXLight)
                .withOpacity(
              chatEntity.isDeleted ?? false ? .5 : 1,
            ),
            hasTail: hasTail,
          ),
          child: Container(
            margin: const EdgeInsets.fromLTRB(10, 2, 2, 2),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  isDeleted
                      ? const SizedBox()
                      : Center(
                          child:
                              _buildMediaGrid(context, images, thumbnailUrl)),
                  if (message.isNotEmpty)
                    Padding(
                      padding:
                          const EdgeInsets.only(bottom: 8, left: 5, top: 10),
                      child: Text(
                        message,
                        style: AppStyles.medium.copyWith(
                          color: (isReceiver
                                  ? context.appColors.blackWhite
                                  : context.appColors.whitePXDark)
                              .withOpacity(
                            isDeleted ? .5 : 1,
                          ),
                        ),
                      ),
                    ),
                  if (!isDeleted) ...[
                    spacerH4,
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          time,
                          style: AppStyles.small.copyWith(
                            color: isReceiver
                                ? context.appColors.greyMP
                                : context.appColors.pLightGrey,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediaGrid(
      BuildContext context, List attachments, List thumbnailUrl) {
    if (attachments.length == 1) {
      return _buildThumbnailWidget(attachments.first,
          thumbnailUrl: thumbnailUrl.first ?? "");
    } else {
      return const SizedBox();
    }
  }

  Widget _buildThumbnailWidget(
    String url, {
    String? thumbnailUrl,
  }) {
    if (isVideo(url)) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: SizedBox(
          width: double.infinity,
          height: 200,
          child: Stack(
            children: [
              Center(
                child: Image.network(
                  thumbnailUrl!,
                  fit: BoxFit.cover,
                ),
              ),
              Align(
                alignment: Alignment.center,
                child: Icon(
                  Icons.play_circle_filled,
                  color: Colors.white.withOpacity(0.8),
                  size: 48,
                ),
              ),
            ],
          ),
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(url, fit: BoxFit.contain),
    );
  }

  bool isVideo(String url) {
    final Uri parsedUri = Uri.parse(url);
    final String path = parsedUri.path;
    final String extension = path.split('.').last.toLowerCase();
    const videoExtensions = ['mp4', 'mov', 'avi', 'mkv'];
    return videoExtensions.contains(extension);
  }
}

class _ImageChatBubbleClipper extends CustomPainter {
  final Color color;
  final bool hasTail;

  _ImageChatBubbleClipper({
    required this.color,
    this.hasTail = true,
  });

  final double _radius = 8;

  @override
  void paint(Canvas canvas, Size size) {
    var h = size.height;
    var w = size.width;

    var path = Path();

    if (hasTail) {
      // Bubble path with tail
      path.moveTo(_radius * 3, 0);
      path.quadraticBezierTo(_radius, 0, _radius, _radius * 1.5);
      path.lineTo(_radius, h - _radius * 1.5);
      path.quadraticBezierTo(_radius * .8, h, 0, h);
      path.quadraticBezierTo(_radius * 1, h, _radius * 1.5, h - _radius * 0.6);
      path.quadraticBezierTo(_radius * 1.5, h, _radius * 3, h);
      path.lineTo(w - _radius * 2, h);
      path.quadraticBezierTo(w, h, w, h - _radius * 1.5);
      path.lineTo(w, _radius * 1.5);
      path.quadraticBezierTo(w, 0, w - _radius * 2, 0);
    } else {
      // Bubble path without tail
      path.moveTo(_radius * 3, 0);
      path.quadraticBezierTo(_radius, 0, _radius, _radius * 1.5);
      path.lineTo(_radius, h - _radius * 1.5);
      path.quadraticBezierTo(_radius, h, _radius * 3, h);
      path.lineTo(w - _radius * 2, h);
      path.quadraticBezierTo(w, h, w, h - _radius * 1.5);
      path.lineTo(w, _radius * 1.5);
      path.quadraticBezierTo(w, 0, w - _radius * 2, 0);
    }

    canvas.clipPath(path);
    canvas.drawRRect(
        RRect.fromLTRBR(0, 0, w, h, Radius.zero),
        Paint()
          ..color = color
          ..style = PaintingStyle.fill);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
