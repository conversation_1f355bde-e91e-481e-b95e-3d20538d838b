import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/common/widgets/common_text_form_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/validators.dart';
import 'package:buyer_board/features/auth/presentation/cubit/register_with_email_password_cubit.dart';
import 'package:buyer_board/features/auth/presentation/screens/auth_screen.dart';
import 'package:buyer_board/features/auth/presentation/state/sign_up_with_email_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/loader.dart';

enum LocalAuthType { none, rememberMe, biometric }

class SignUpForm extends StatefulWidget {
  const SignUpForm({super.key});

  @override
  State<SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<SignUpForm> {
  final emailTextController = TextEditingController();
  final passwordTextController = TextEditingController();
  final confirmPasswordTextController = TextEditingController();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  bool hasBiometricSupport = false;
  LocalAuthType localAuthtype = LocalAuthType.none;
  final _formKey = GlobalKey<FormState>();
  String? emailerror;

  @override
  void initState() {
    context.read<SignUpWithEmailCubit>().canAuthenticate().then((value) {
      hasBiometricSupport = value;
      if (mounted) {
        setState(() {});
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    emailTextController.dispose();
    passwordTextController.dispose();
    confirmPasswordTextController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    super.dispose();
  }

  void toggleLocalAuthType(LocalAuthType type) {
    localAuthtype = localAuthtype != type ? type : LocalAuthType.none;
    setState(() {});
  }

  void onSignMeUp() {
    if (passwordTextController.text == confirmPasswordTextController.text) {
      final String email = emailTextController.text;
      final password = passwordTextController.text;
      context.read<SignUpWithEmailCubit>().registerWithEmailAndPassword(
            context,
            email: email.trim(),
            password: password,
            firstName: firstNameController.text.trim(),
            lastName: lastNameController.text.trim(),
            rememberMe: localAuthtype == LocalAuthType.rememberMe,
            enabledBiometric: localAuthtype == LocalAuthType.biometric,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SignUpWithEmailCubit, SignUpWithEmailState>(
      listener: (context, state) {
        state.mapOrNull(
          loading: (state) => Loader.show(),
          success: (state) {
            Loader.hide();
            context
              ..showToast(message: state.message, isSuccess: true)
              ..go(PagePath.mainScreen);
          },
          rememberMe: (state) {
            Loader.hide();
            context
              ..showToast(message: state.message, isSuccess: true)
              ..go(PagePath.mainScreen);
          },
          signupError: (state) {
            setState(() {
              emailerror = state.error!;
            });
            Loader.hide();
            context.showToast(message: state.error, isSuccess: false);
          },
        );
      },
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.signUpWithEmailPassword,
              style: AppStyles.mediumSemiBold.copyWith(color: AppColors.white),
            ),
            const SizedBox(height: 16),
            CommonTextFormField(
              label: 'First Name',
              controller: firstNameController,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'First Name is required';
                }
                return null;
              },
              borderRadius: BorderRadius.circular(4),
              contextTextColor: context.colorScheme.onPrimaryFixed,
              errorTextColor: AppColors.white,
              outlineErrorBorder: true,
              showErrorPrefixIcon: true,
            ),
            const SizedBox(height: 16),
            CommonTextFormField(
              label: 'Last Name',
              controller: lastNameController,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Last Name is required';
                }
                return null;
              },
              borderRadius: BorderRadius.circular(4),
              contextTextColor: context.colorScheme.onPrimaryFixed,
              errorTextColor: AppColors.white,
              outlineErrorBorder: true,
              showErrorPrefixIcon: true,
            ),
            const SizedBox(height: 16),
            CommonTextFormField(
              label: Strings.emailAddress,
              controller: emailTextController,
              validator: Validators().emailValidator,
              borderRadius: BorderRadius.circular(4),
              errorTextColor: AppColors.white,
              outlineErrorBorder: true,
              contextTextColor: context.colorScheme.onPrimaryFixed,
              showErrorPrefixIcon: true,
              errorText: emailerror,
            ),
            const SizedBox(height: 16),
            CommonTextFormField(
              label: Strings.password,
              controller: passwordTextController,
              validator: Validators().passwordValidator,
              borderRadius: BorderRadius.circular(4),
              contextTextColor: context.colorScheme.onPrimaryFixed,
              keyboardType: TextInputType.visiblePassword,
              errorTextColor: AppColors.white,
              outlineErrorBorder: true,
              showErrorPrefixIcon: true,
            ),
            const SizedBox(height: 16),
            CommonTextFormField(
              label: Strings.reEnterPassword,
              controller: confirmPasswordTextController,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please re-enter your password';
                } else if (value != passwordTextController.text) {
                  return 'Passwords do not match';
                }
                return null;
              },
              borderRadius: BorderRadius.circular(4),
              contextTextColor: context.colorScheme.onPrimaryFixed,
              // obscure: true,
              keyboardType: TextInputType.visiblePassword,
              errorTextColor: AppColors.white,
              outlineErrorBorder: true,
              showErrorPrefixIcon: true,
            ),
            const SizedBox(height: 25),
            CommonButton.basic(
              label: Strings.signMeUp,
              action: () {
                if (_formKey.currentState?.validate() ?? false) {
                  onSignMeUp();
                }
              },
              backgroundColor: AppColors.white,
              textColor: AppColors.primary,
            ),
            CommonButton.basic(
              label: Strings.loginInstead,
              action: () => authActionNotifier.value = AuthAction.none,
              backgroundColor: AppColors.primary,
              textColor: AppColors.white,
            ),
          ],
        ),
      ),
    );
  }
}

class CustomCheckBoxTile extends StatelessWidget {
  const CustomCheckBoxTile(
      {super.key,
      required this.onChanged,
      required this.value,
      required this.label});
  final bool value;
  final String label;
  final void Function(bool?)? onChanged;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onChanged?.call(!value),
      child: Material(
        color: Colors.transparent,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: Theme(
                data: ThemeData(unselectedWidgetColor: Colors.white),
                child: Checkbox(
                  side: const BorderSide(color: AppColors.white, width: 2),
                  checkColor: AppColors.primary,
                  activeColor: AppColors.white,
                  value: value,
                  onChanged: onChanged,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: AppStyles.mediumSemiBold.copyWith(color: AppColors.white),
            ),
          ],
        ),
      ),
    );
  }
}
