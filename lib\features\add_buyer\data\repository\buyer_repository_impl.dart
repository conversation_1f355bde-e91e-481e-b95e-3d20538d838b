import 'package:buyer_board/core/network/rest_api_client.dart';
import 'package:buyer_board/features/add_buyer/domain/repository/buyer_repository.dart';

import '../../../../core/network/base_response.dart';
import '../models/buyer_model.dart';

class BuyerRepositoryImpl extends BuyerRepository {
  RestAPIClient restAPIClient;
  BuyerRepositoryImpl({required this.restAPIClient});
  @override
  Future<BaseResponse<BuyerModel>> addBuyer({required BuyerModel buyer}) async {
    return await restAPIClient.addBuyer(buyer: buyer);
  }

  @override
  Future<BaseResponse<BuyerModel>> editBuyer(
      {required BuyerModel buyer, required int id}) async {
    return await restAPIClient.editBuyer(buyer: buyer, id: id);
  }

  @override
  Future<BaseResponse<BuyerModel>> favouriteBuyer(
      {required bool isFavourite, required int id}) {
    return restAPIClient
        .favouriteBuyer(isFavourite: {"is_favourite": isFavourite}, id: id);
  }

  @override
  Future<BaseResponse<List<BuyerModel>>> getAllBuyers() async {
    return await restAPIClient.getAllBuyers();
  }

  @override
  Future<BaseResponse<List<BuyerModel>>> getFavouriteBuyers() async {
    return await restAPIClient.getFavouriteBuyers();
  }

  @override
  Future<BaseResponse<List<BuyerModel>>> getMyBuyers() async {
    return await restAPIClient.getMyBuyers();
  }

  @override
  Future<BaseResponse<List<BuyerModel>>> getOtherBuyers() async {
    return await restAPIClient.getOtherBuyers();
  }

  @override
  Future<BaseResponse<List<BuyerModel>>> getBuyersByZipCode(
      String zipCode) async {
    return await restAPIClient.getBuyersByZipCode(zipCode: zipCode);
  }

  @override
  Future<BuyerModel> updateNote({required int id, required String note}) async {
    final response = await restAPIClient.updateNote(
      id: id,
      content: note,
    );
    return response.data ?? BuyerModel();
  }

  @override
  Future<BaseResponse<BuyerModel>> getBuyer(int id) async {
    return await restAPIClient.getBuyer(id: id);
  }
}
