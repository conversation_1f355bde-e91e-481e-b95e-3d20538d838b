import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

sealed class LocationState {}

class LocationDataState extends LocationState {
  final LocationEntity? location;

  LocationDataState(this.location);
}

class LocationLoadingState extends LocationState {}

class CurrentLocationCubit extends Cubit<LocationState> {
  CurrentLocationCubit() : super(LocationLoadingState());

  void update(LocationEntity? location) {
    emit(LocationDataState(location));
  }
}
