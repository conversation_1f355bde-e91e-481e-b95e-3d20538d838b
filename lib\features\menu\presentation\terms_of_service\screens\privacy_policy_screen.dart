import 'package:buyer_board/common/widgets/application_bar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/features/menu/presentation/widgets/bullet_point_widget.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  final noticePoints = const [
    'The web Platforms and services covered by this Policy',
    'Our policies concerning usage of our Platform',
    'What personal information we collect from you',
    'What non-personal information we collect from you',
    'Information on “cookies” and related technology',
    'How we use and share the information that we collect from you',
    'Information about links and third parties related to our Platforms and services',
    'How you may access or change the information you have provided to us',
    'Other important agreements that govern your use of our Platforms and services',
    'Information for persons outside the United States',
    'Our commitment to secure the personal information we have collected',
    'Notice of changes to the Policy',
  ];

  final subjectDataRightsPoints = const [
    'Right to be informed: BB is open as to what data is being collected, how it’s being used, how long it will be kept and whether it will be shared with any third parties.',
    'Right of access: BB advises that individual users have the right to request a copy of the information that BB holds on them.',
    'Right of rectification: BB allows individual users the right to correct any data that is inaccurate or incomplete.',
    'Right to be forgotten: In certain circumstances, individual users can ask BB for the data BB holds on them to be erased from their records.',
    'Right of portability: Individual users can request that BB transfer any data that it holds on them to another company.',
    'Right to restrict processing: Individual users can request that BB limit the way it uses their personal data.',
    'Right to object: Individual users have the right to challenge certain types of processing, such as direct marketing, including unsubscribing.',
    'Right related to automated decision making including profiling: Individual users are free to request a review of automated processing if they believe BB\'s own rules aren’t being properly followed internally.',
  ];

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final colors = context.appColors;
    return Scaffold(
      appBar: const ApplicationBar(
        title: 'Privacy Policy',
        subtitle: 'About BuyerBoard',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          children: [
            RichText(
              text: TextSpan(
                style: typography.smallReg.copyWith(
                  color: colors.greyDefaultGreyMedium,
                ),
                children: [
                  const TextSpan(
                    text: 'Last Revised: 10 September, 2024\n\n',
                  ),
                  TextSpan(
                    text:
                        'YOUR USE OF OUR SERVICES, SERVICES AND WEB PLATFORMS IS SUBJECT TO YOUR AGREEMENT TO THE TERMS OF THIS PRIVACY POLICY.\n\n',
                    style: typography.smallSemi,
                  ),
                  const TextSpan(
                    text:
                        'Buyer Board, LLC. (“BB”) respects your privacy and is committed to protecting the personal information that you may provide while using our web Platforms and services. This Privacy Policy (the “Policy”) is intended to provide you with notice of the following:\n',
                  ),
                ],
              ),
            ),
            for (final point in noticePoints)
              BulletPointWidget(
                text: point,
              ),
            RichText(
              text: TextSpan(
                style: typography.smallReg.copyWith(
                  color: colors.greyDefaultGreyMedium,
                ),
                children: [
                  const TextSpan(
                    text:
                        'If you have any questions, complaints, suggestions or comments regarding this Policy, please contact the BB Customer Service Team at ',
                  ),
                  TextSpan(
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        CoreUtils.launchEmailClient(context: context);
                      },
                    text: '<EMAIL>',
                    style: context.typography.smallReg.copyWith(
                      color: context.appColors.pPXLight,
                    ),
                  ),
                  const TextSpan(
                    text:
                        '\n\nThe web Platforms and services covered by this Policy\n\n',
                  ),
                  const TextSpan(
                    text:
                        'We currently operate the following Platform (or “Platforms”) and services (“Service”, or “Services”):\n\n',
                  ),
                  WidgetSpan(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 32),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Buyer Board (iOS / Apple)',
                            style: typography.smallReg.copyWith(
                              color: colors.greyDefaultGreyMedium,
                            ),
                          ),
                          Text(
                            'Buyer Board (Android / Google)',
                            style: typography.smallReg.copyWith(
                              color: colors.greyDefaultGreyMedium,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const TextSpan(
                    text:
                        '\n\nIn addition, we own other domain names that may redirect to the',
                  ),
                  TextSpan(
                    text: ' www.BuyerBoard.com ',
                    style: typography.smallReg.copyWith(
                      color: colors.pPXLight,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        CoreUtils.launchUri(
                            context: context,
                            uri: Uri.parse('https://www.BuyerBoard.com'));
                      },
                  ),
                  const TextSpan(
                      text:
                          ' web site and/or content-specific “subdomains” of the web site. \n\n'),
                  const TextSpan(
                    text:
                        'From time to time, we may add new Platforms that may not be listed above, but they will provide a link to this Policy and will be governed by its terms. \n\n',
                  ),
                  TextSpan(
                    text: 'Our policies concerning use of the Platform\n\n',
                    style: typography.smallSemi.copyWith(
                      color: colors.greyDefaultGreyMedium,
                    ),
                  ),
                  const TextSpan(
                    text:
                        'Users of the Platform are required to be Licensed Realtors with an Active (in good standing) License.\n\n',
                  ),
                  TextSpan(
                    text: 'Persons under the Age of 18 \n\n',
                    style: typography.smallSemi.copyWith(
                      color: colors.greyDefaultGreyMedium,
                    ),
                  ),
                  const TextSpan(
                      text:
                          'Because our Platforms and Services are not intended for use by children, BB does not provide memberships to persons under age 18, even if they are Licensed Realtors. If you are under the age of 18, you (1) are expressly disallowed to use our Services or create an account on them and (2) may not sign up for any subscription service. Except as may be required by law, BB will not knowingly collect, maintain, or disclose any personal information from children under the age of 18. \n\n'),
                  TextSpan(
                    text:
                        'Personal Information: General and GDPR Compliance\n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'We do not collect any personal information from you unless you elect to provide it to us voluntarily. If you decide to provide us with any personal information (e.g., name, email address, and mailing address), we only collect the minimum possible personal information necessary to fulfill your requests and our legitimate business objectives. If, when requested, you refuse to divulge personal information of the sort that falls within this minimum-required amount, you will not be able to access certain areas on the Platforms (such as the message boards). Some information, such as your originating IP address (if not using a VPN) is part and parcel of every internet communication, and so is "provided" automatically when you create an Account, but BB considers your IP Address(es) to be personal data as well. In the case of an official request from you to delete all of your Accounts and Personal Data (via the option provided for this purpose in the Settings of the Application), your IP Account record is also expunged along with all other Account Data. Consequently, BB maintains full UK and EU General Data Protection Regulation (GDPR) Compliance. \n\n'),
                  TextSpan(
                    text: 'Memberships and Registration \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'In order for you to register with BB, we require you to provide your Name and Real Estate License number. Your provided name will be available to other Users of our Platform while you participate in some services, like inter-User chat or in your postings. Personal information voluntarily disclosed by you within such chat areas, message services or during use of the Platform can potentially be collected by other Users and may result in unsolicited messages. We are not responsible for protecting such information that you may choose to voluntarily disclose to third parties through our services (e.g. sending your telephone number to another user through the chat service). To the extent that you create a public profile that may be available to others on our Platforms and services, you are solely responsible for its content and accuracy at all times. To edit, change, or delete such information, follow the instructions provided within that relevant service. \n\n'),
                  TextSpan(
                    text: 'Emails from BuyerBoard \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                    text:
                        'You must provide an e-mail address to us in order to receive emails from us. All e-mail received from BB is, consequently, "opt-in". You can always "opt out" of receiving future mailings resulting from this by following the directions contained in any received email to “unsubscribe.” \n\n',
                  ),
                  TextSpan(
                    text: 'Technical Support \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'Issues related to customer service, billing questions, or technical and computer support, etc. may also require you to provide us with some personal information (such as such as your real name, email address, street address, etc.) so that we may resolve the issue properly.  \n\n'),
                  TextSpan(
                    text: 'Online Services \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'When you use any of the Services offered and/or served by BB or use any of the communication features on the Platforms, your IP address (the Internet protocol address from which you access any of the above) may be stored in our records. When any portion of the platform is updated or “patched,” your device may automatically check to see that you have the most recent version of platform-specific files and even automatically update your existing version to a newer version, depending on your settings. When you communicate within any platform, even “privately” to another person, you do so with the understanding that those communications go through our servers and could potentially be monitored by us. Consequently, you should have no expectation of privacy in any of those communications and, accordingly, you expressly consent to our monitoring of any / all communications (including technical support and customer service communications) that you send and receive through our services. When you log into one of our online Services, your device specifications (such as its Operating System version, CPU type, RAM, etc.) may be reviewed/recorded by us for the purposes of analyzing and optimizing your platform experience and in order to provide you with customer service. \n\n'),
                  TextSpan(
                    text:
                        'What non-personal information we collect from you \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'General, aggregated, demographic, and non-personal information may be collected by BB. It will not be linked to any of your personal information, through cookies or other means, without your consent. This type of anonymous, aggregated profiling and session data may include information that you have provided to us through surveys, polls, etc., but will not be tied to any personal information without your consent. It may also include aggregated anonymous information about site usage and the customer base.\n\n'
                          'In many cases, BB will automatically collect certain non-personal information about your use of its Platforms and services. BB might collect, among other things, information concerning the operating system your device is using, the domain name of your Internet service provider, which functions within the BuyerBoard platform you use most frequently, and any web site or advertisement that was linked to or from the BB site when you first downloaded the Application. To do this, BB may use cookies and other technology (see below). Your visits to our web Platforms, and information provided through these technologies, will be anonymous unless you provide us with personal information or have provided such information in the past. This information is anonymized and used on a statistical basis for further improving BB\'s services and offerings.  \n\n'),
                  TextSpan(
                    text: 'Information on cookies and related technology  \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'BB web Platforms, and some services and advertisements on such Platforms, may contain “cookies.” A cookie is a small data packet that is sent from BB\'s servers over the Internet connection to your device, which will temporarily store the cookie if it is set to accept such cookies. Upon request by BB’s servers, your device is subsequently able to present that cookie back to securely prove its trusted identity, which is at times necessary when switching between different portions of the Platforms as well as different BB servers. Clear GIFs (also known as “web beacons”) assist in the delivery of cookies.\n\n'
                          'BB uses cookies, clear GIFs, frames, server log analysis and other technology to optimize and customize your experience with our various Platforms and services. Any personal information that is incidentally collected through the use of these technologies will be handled according to this Policy.\n\n'
                          'Cookies are passive things. They are not programs. They are akin to an ID card that is issued to your device and can then be subsequently presented by your device to prove its identity (for example, to prevent repeatedly having to enter your username and password when switching between pages). Cookies were invented in 1994, and today are one of the most common web technologies on the Internet.\n\n'
                          'Most devices and even internet browsers allow you the full power to erase cookies from your device, block the acceptance of new cookies, or receive a warning and choice-to-approve before any given cookie is stored. You should refer to your device and/or browser instructions or “Help” screen to learn more about how to manage cookies. Please note, however, that if you choose to block cookies, the portions of the BB web Platforms and services that rely on cookies may no longer function properly. \n\n'),
                  TextSpan(
                    text:
                        'How we use and share the information that we collect from you  \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'Personal information which we collect for a particular purpose will only be saved and used for that purpose, unless you have agreed to allow us to use it for some other purpose, as described in this Policy.\n\n'
                          'When you have provided personal information to us for a particular purpose, we may disclose your information to other companies that we have engaged to assist us in fulfilling your request. We may also disclose any of your personal information to law enforcement or other appropriate third parties in connection with criminal investigations, investigation of fraud, infringement of intellectual property rights, or other suspected illegal activities, or as otherwise may be required by applicable law, or, as we deem necessary in our sole discretion, in order to protect the legitimate legal and business interests of BB. \n\n'
                          'We may use the information we collect from you for the purpose of providing you with technical support, customer service, and account maintenance. We use the information we collect to learn what you like, tailor your experience accordingly, and to improve web Platforms and our other products. We send e-mail to our members who want to receive e-mail from us. If you wish to stop receiving e-mails from us, you may choose to opt out by following the steps for that purpose described in each such e-mail. \n\n'
                          'Except as described in this policy, we will not give any of your personal information to any third-party without your express approval, and the only personal information we will ask you if you want us to share with third parties is your name, your mailing address and e-mail address. We sometimes share general, demographic, or aggregated (not personal) information with third parties about our user base, but that information does not include any personal information. \n\n'
                          'You will be deemed to have consented to the disclosure to, and use by, a subsequent owner or operator of the Platforms, of any personal information contained in the BB database for such site, if BB or one of its companies assigns all of its rights and obligations regarding the use of your personal information at the time of a bankruptcy, merger, acquisition, sale of all or substantially all of BB’s or such BB company’s assets to a subsequent owner or operator, or similar event. \n\n'),
                  TextSpan(
                    text:
                        'Information about links and third parties related to our Platforms and services   \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'This Policy applies only to information collected online by BB through web Platforms '
                          'maintained by BB and affiliated companies that BB may own or control. From time to time, '
                          'we may provide links to third-party web Platforms. This Policy does not apply to third-party web Platforms, '
                          'whether we provide a link to such Platforms or not, so we encourage you to review the policies of all such '
                          'Platforms carefully, as your rights will be governed by those third-party policies and not by BB’s. Additionally, '
                          'there may be unauthorized third-party web Platforms that may be providing links from their Platforms to our web Platforms '
                          'without our knowledge or control. We are not responsible for the content of any third-party web Platforms, expressly '
                          'disclaim any statements or assertions made on such web Platforms, and deny all liability associated with your use of, '
                          'and the content on, such other Platforms or advertisements contained therein. \n\n'),
                  TextSpan(
                    text:
                        'How you may access, change, or delete the information you have provided to us   \n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'If you change your mind and do not want to receive e-mails from us and/or to have us share your name and e-mail address (or other personal information) with third parties, you may send an e-mail to us at '),
                  TextSpan(
                    text: '<EMAIL>',
                    style: typography.smallReg.copyWith(
                      color: colors.pPXLight,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        CoreUtils.launchEmailClient(context: context);
                      },
                  ),
                  const TextSpan(
                      text:
                          '. Subject to security and privacy concerns (e.g., regarding your password, etc.), we will give you the ability to review, correct, delete, and/or update your information. To do so, you may send us an e-mail at '),
                  TextSpan(
                    text: '<EMAIL>',
                    style: typography.smallReg.copyWith(
                      color: colors.pPXLight,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        CoreUtils.launchEmailClient(context: context);
                      },
                  ),
                  const TextSpan(
                      text:
                          '. You may also contact us via physical mail at the following address:  \n\n'),
                  WidgetSpan(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Attention: Privacy Officer',
                            style: typography.smallReg.copyWith(
                              color: colors.greyDefaultGreyMedium,
                            ),
                          ),
                          Text(
                            'Buyer Board, LLC.',
                            style: typography.smallReg.copyWith(
                              color: colors.greyDefaultGreyMedium,
                            ),
                          ),
                          Text(
                            '16 Malaga Cove Plaza',
                            style: typography.smallReg.copyWith(
                              color: colors.greyDefaultGreyMedium,
                            ),
                          ),
                          Text(
                            'Palos Verdes Estates  CA  90274',
                            style: typography.smallReg.copyWith(
                              color: colors.greyDefaultGreyMedium,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const TextSpan(
                      text:
                          '\n\nIf you choose to have your personal information removed from our active databases, we will take prompt action to do so within a reasonable time after your request and we will take all reasonable steps to ensure that your personal data is not used (except as may be required by law, or in an anonymized fashion) by BB after we have received your notification. \n\n '),
                  TextSpan(
                    text:
                        'Other important agreements that govern your use of our Platforms and services\n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'In addition to this Policy, your rights and obligations concerning our web Platforms, services, and online Services are governed by the applicable web site terms of service, the applicable platform’s end user license agreement and/or terms of use, and other applicable policies, guidelines, and requirements. When you sign up to use our services, please review each such agreement carefully, as you must agree to be bound by each such agreement before you may use the service. \n\n'),
                  TextSpan(
                    text:
                        'Information for persons outside the United States\n\n',
                    style: typography.smallSemi
                        .copyWith(color: colors.greyDefaultGreyMedium),
                  ),
                  const TextSpan(
                      text:
                          'By consenting to this Policy, you confirm that your command and knowledge of English is sufficient to understand the terms and conditions set forth herein.\n\n'
                          'For persons from European Union countries, as used herein, terms such as “personal information” refer to “personal data” as defined by the Directive 95/46/EC of the European Parliament and of the Council of 24 October 1995 on the protection of individuals with regard to the processing of personal data and on the free movement of such data (“Directive”). We will not collect any personal data from you unless you provide it voluntarily by providing us with your freely given specific and informed consent. By using BB Platforms or services, you agree that we may transfer your personal information outside the European Union in connection with the purposes stated in this Policy. Your personal data may be transferred to our servers in the United States (and/or other non-EU jurisdictions, as applicable).\n\n'
                          'In certain countries other than the United States, some of our online Services and the services related thereto, may be provided to you by third-party distribution partners. To the extent that a third-party distributor is providing services related to your account, you understand and agree that each such distributor will collect and use your personal information in accordance with its own privacy policies (which you will have access to when you register to play). To the extent that BB collects or retains any of your personal information, its use will continue to be governed by the terms of this Policy. \n\n'
                          'In compliance with the EU GPDR, BB provides eight subject data rights to its users:\n'),
                ],
              ),
            ),
            for (final point in subjectDataRightsPoints)
              BulletPointWidget(
                text: point,
              ),
            RichText(
              // This is the last RichText widget in the PrivacyPolicyScreen
              text: TextSpan(
                style: typography.smallReg.copyWith(
                  color: colors.greyDefaultGreyMedium,
                ),
                children: [
                  const TextSpan(
                    text:
                        '\nAll individual users of BB services are free to exercise their rights and can contact us via e-mail or physical mail at the above-provided addresses in order to do so.\n\n'
                        'Please note that the "right to be forgotten" does not fully encompass historical financial transactions, which may be required by law to be tracked for purposes of taxation.\n\n',
                  ),
                  TextSpan(
                    text:
                        'Our commitment to secure the personal information we have collected \n\n',
                    style: typography.smallSemi.copyWith(
                      color: colors.greyDefaultGreyMedium,
                    ),
                  ),
                  const TextSpan(
                      text:
                          'While BB takes best-practice precautions against possible breaches in its web Platforms and customer databases, utilizing current SSL Encryption, IP Address restriction, Firewalls with Stateful Packet Inspection, Port Restriction and much more, no web site or Internet transmission is completely secure. Zero-day exploits continue to be found that allow for unexpected breaches to occur even when all known, current threats have been countered. Consequently, neither BB nor any other online service provider can fully guarantee that unauthorized access, hacking, data loss, or other breaches will never occur. Consequently, you expressly acknowledge that your use of all BB Platforms and services is wholly at your own risk, and agree to hold BB harmless from any liability that could potentially descend from any such unauthorized access, hacking, data loss, or other breaches. BB urges you to take steps to keep your personal information safe by choosing a unique password for their services, memorizing that password or (if written down) keeping it in a safe place separate from your account information, logging out of your user account whenever you are finished using BB services, and fully closing our Services, then locking your devices so that a passcode is required before entry. \n\n'
                          'Whenever you give BB sensitive or confidential information, BB will take commercially reasonable steps to protect the information by establishing a secure connection with your device. BB also utilizes third parties to handle extremely confidential information such as credit card data, and as per best practices, does not store or retain such highly confidential data at all.\n\n'),
                  TextSpan(
                    // This is the last TextSpan in the PrivacyPolicyScreen
                    text: 'Notice of changes to the Policy  \n\n',
                    style: typography.smallSemi.copyWith(
                      color: colors.greyDefaultGreyMedium,
                    ),
                  ),
                  const TextSpan(
                      text:
                          'BB reserves the right to update this Policy at any time by notifying registered users via e-mail of the existence of a new Policy and/or posting the new Policy on the BB Platforms. All changes to the Policy will be effective when posted, and your continued use of any BB site or service after the posting will constitute your acceptance of, and agreement to be bound by, those changes. \n\n'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
