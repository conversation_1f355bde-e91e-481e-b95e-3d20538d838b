abstract class ChatPayload {
  final int userId;
  final String topic;
  final String event;
  final int ref;

  ChatPayload({
    required this.userId,
    required this.topic,
    required this.event,
    required this.ref,
  });

  Map<String, dynamic> toJson();
}

abstract class ChatArchiveBase extends ChatPayload {
  final int threadId;

  ChatArchiveBase({
    required this.threadId,
    required int userId,
    required String event,
  }) : super(
          userId: userId,
          topic: 'chat_room:$userId',
          event: event,
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {
        'thread_id': threadId,
      },
    };
  }
}

final class ChatArchivePayload extends ChatArchiveBase {
  ChatArchivePayload({
    required int threadId,
    required int userId,
  }) : super(
          threadId: threadId,
          userId: userId,
          event: 'archive_chat',
        );
}

final class ChatUnarchivePayload extends ChatPayload {
  final int threadId;
  ChatUnarchivePayload({
    required this.threadId,
    required int userId,
  }) : super(
          topic: 'archive_chat_room:$userId',
          userId: userId,
          event: 'unarchive_chat',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {
        'thread_id': threadId,
      },
    };
  }
}

final class DeleteMessagesPayload extends ChatPayload {
  final int otherUserId;
  final int buyerId;
  final Set<int> messageIds;

  DeleteMessagesPayload({
    required int userId,
    required this.otherUserId,
    required this.buyerId,
    required this.messageIds,
  }) : super(
          userId: userId,
          topic: 'chat:$userId, $otherUserId, $buyerId',
          event: 'delete_message',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {
        'first_user_id': userId,
        'second_user_id': otherUserId,
        'buyer_id': buyerId,
        'message_ids': messageIds.toList(),
      },
    };
  }
}

final class SendMessagePayload extends ChatPayload {
  final int otherUserId;
  final int buyerId;
  final String message;
  final int offset;
  final List<Map<String, dynamic>>? attachments;

  SendMessagePayload({
    required int userId,
    required this.otherUserId,
    required this.buyerId,
    required this.message,
    required this.offset,
    this.attachments,
  }) : super(
          userId: userId,
          topic: 'chat:$userId, $otherUserId, $buyerId',
          event: 'new_message',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {
        'first_user_id': userId,
        'second_user_id': otherUserId,
        'buyer_id': buyerId,
        'sent_by': userId,
        'received_by': otherUserId,
        'content': message,
        'timezone_offset': offset,
        "attachments": attachments,
      },
    };
  }
}

final class EditMessagePayload extends ChatPayload {
  final int otherUserId;
  final int buyerId;
  final int messageId;
  final String message;

  EditMessagePayload({
    required int userId,
    required this.otherUserId,
    required this.buyerId,
    required this.messageId,
    required this.message,
  }) : super(
          userId: userId,
          topic: 'chat:$userId, $otherUserId, $buyerId',
          event: 'edit_message',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {
        'first_user_id': userId,
        'second_user_id': otherUserId,
        'buyer_id': buyerId,
        'message_id': messageId,
        'content': message,
      },
    };
  }
}

final class SendReplyMessagePayload extends ChatPayload {
  final int otherUserId;
  final int buyerId;
  final String message;
  final int parentId;

  SendReplyMessagePayload({
    required int userId,
    required this.otherUserId,
    required this.buyerId,
    required this.message,
    required this.parentId,
  }) : super(
          userId: userId,
          topic: 'chat:$userId, $otherUserId, $buyerId',
          event: 'new_message',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {
        'first_user_id': userId,
        'second_user_id': otherUserId,
        'buyer_id': buyerId,
        'sent_by': userId,
        'received_by': otherUserId,
        'content': message,
        'parent_id': parentId,
      },
    };
  }
}

final class GetAllChatsPayload extends ChatPayload {
  GetAllChatsPayload({
    required int userId,
  }) : super(
          userId: userId,
          topic: 'chat_room:$userId',
          event: 'phx_join',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {},
    };
  }
}

final class GetAllArchiveChatsPayload extends ChatPayload {
  GetAllArchiveChatsPayload({
    required int userId,
  }) : super(
          userId: userId,
          topic: 'archive_chat_room:$userId',
          event: 'phx_join',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {},
    };
  }
}

final class GetChatPayload extends ChatPayload {
  final int otherUserId;
  final int buyerId;

  GetChatPayload({
    required int userId,
    required this.otherUserId,
    required this.buyerId,
  }) : super(
          userId: userId,
          topic: 'chat:$userId, $otherUserId, $buyerId',
          event: 'phx_join',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {
        'first_user_id': userId,
        'second_user_id': otherUserId,
        'buyer_id': buyerId,
      },
    };
  }
}

final class ChatNotificationsPayload extends ChatPayload {
  ChatNotificationsPayload({
    required int userId,
  }) : super(
          userId: userId,
          topic: 'chat_notifications:$userId',
          event: 'phx_join',
          ref: 0,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'topic': topic,
      'event': event,
      'ref': ref,
      'payload': {},
    };
  }
}
