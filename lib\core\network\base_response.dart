import 'package:json_annotation/json_annotation.dart';
part 'base_response.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
  genericArgumentFactories: true,
  explicitToJson: true,
)
class BaseResponse<T> {
  const BaseResponse({required this.data, required this.message});
  final T? data;
  final BaseMessage message;
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$BaseResponseToJson(this, toJsonT);
  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$BaseResponseFromJson(json, fromJsonT);
}

@JsonSerializable(
  fieldRename: FieldRename.snake,
  explicitToJson: true,
)
class BaseMessage {
  const BaseMessage({this.title, this.body});
  final String? title;
  final String? body;
  Map<String, dynamic> toJson() => _$BaseMessageToJson(this);
  factory BaseMessage.fromJson(Map<String, dynamic> json) =>
      _$BaseMessageFromJson(json);
}
