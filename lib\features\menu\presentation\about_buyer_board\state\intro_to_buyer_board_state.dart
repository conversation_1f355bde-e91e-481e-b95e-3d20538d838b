import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../onboarding/data/onboarding_model.dart';

part 'intro_to_buyer_board_state.freezed.dart';

@freezed
class IntroToBuyerBoardState with _$IntroToBuyerBoardState {
  const factory IntroToBuyerBoardState.initial() = initial;
  const factory IntroToBuyerBoardState.loading() = loading;
  const factory IntroToBuyerBoardState.success(List<OnboardingModel> onBoardings) = success;
  const factory IntroToBuyerBoardState.onBoardingError(String? error) =
  onBoardingError;
}