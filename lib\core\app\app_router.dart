import 'package:buyer_board/core/extensions/string_extension.dart';
import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/add_buyer/presentation/screens/add_buyer_screen.dart';
import 'package:buyer_board/features/auth/presentation/screens/auth_screen.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/presentation/screens/attachment_detail_screen.dart';
import 'package:buyer_board/features/chat/presentation/screens/chat_archive_screen.dart';
import 'package:buyer_board/features/chat/presentation/screens/chat_details_screen.dart';
import 'package:buyer_board/features/chat/presentation/screens/chat_main_screen.dart';
import 'package:buyer_board/features/chat/presentation/widgets/image_details.dart';
import 'package:buyer_board/features/filters/presentation/screens/new_filter_screen.dart';
import 'package:buyer_board/features/forget_password/presentation/screens/forget_password_screen.dart';
import 'package:buyer_board/features/forget_password/presentation/screens/update_password_screen.dart';
import 'package:buyer_board/features/home/<USER>/screens/expanded_buyer_card.dart';
import 'package:buyer_board/features/intro/presentation/screens/app_onboarding_screen.dart';
import 'package:buyer_board/features/menu/presentation/faq/screens/faq_screen.dart';
import 'package:buyer_board/features/menu/presentation/help/screens/topic_screen.dart';
import 'package:buyer_board/features/menu/presentation/about_buyer_board/screens/about_buyer_board_screen.dart';
import 'package:buyer_board/features/menu/presentation/help/screens/help_screen.dart';
import 'package:buyer_board/features/main/presentation/screens/main_screen.dart';
import 'package:buyer_board/features/menu/presentation/screens/menu_screen.dart';
import 'package:buyer_board/features/menu/presentation/terms_of_service/screens/privacy_policy_screen.dart';
import 'package:buyer_board/features/menu/presentation/privacy_policy/screens/terms_of_service_screen.dart';
import 'package:buyer_board/features/onboarding/presentation/screens/onboarding_screen.dart';
import 'package:buyer_board/features/settings/presentation/screens/settings_screen.dart';
import 'package:buyer_board/features/settings/presentation/screens/update_login_credentials_screen.dart';
import 'package:buyer_board/features/splash/presentation/screens/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/chat/data/models/chat_buyer_model.dart';
import '../../features/forget_password/presentation/screens/pin_code_screen.dart';
import '../../features/menu/data/models/help_response.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();

class AppRouter {
  AppRouter._();
  static final router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    debugLogDiagnostics: false,
    routes: [
      GoRoute(
        path: PagePath.splash,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: PagePath.authScreen,
        builder: (context, state) => AuthScreen(
            localAuthEnabled:
                state.extra != null ? state.extra as bool : false),
      ),
      GoRoute(
        path: PagePath.forgetPassword,
        builder: (context, state) => const ForgetPasswordScreen(),
        routes: [
          GoRoute(
            path: PagePath.pinCodeScreen.pathLocation,
            builder: (context, state) =>
                PinCodeScreen(email: state.extra as String),
          ),
        ],
      ),
      GoRoute(
        path: PagePath.updatePasswordScreen,
        builder: (context, state) => UpdatePasswordScreen(
          email: state.extra as String,
        ),
      ),
      GoRoute(
        path: PagePath.onboardingScreen,
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: PagePath.chatArchiveScreen,
        builder: (context, state) => const ChatArchiveScreen(),
      ),
      GoRoute(
        path: PagePath.menu,
        builder: (context, state) => const MenuScreen(),
        routes: [
          GoRoute(
            path: PagePath.settingsScreen,
            name: PagePath.settingsScreen,
            builder: (context, state) => const SettingsScreen(),
            routes: [
              GoRoute(
                path: PagePath.updateLoginCredentialsScreen,
                name: PagePath.updateLoginCredentialsScreen,
                builder: (context, state) =>
                    const UpdateLoginCredentialsScreen(),
              )
            ],
          )
        ],
      ),
      GoRoute(
        path: PagePath.aboutBuyerBoard,
        builder: (context, state) => const AboutBuyerBoardScreen(),
      ),
      GoRoute(
        path: PagePath.termsOfService,
        builder: (context, state) => const TermsOfServiceScreen(),
      ),
      GoRoute(
        path: PagePath.privacyPolicy,
        builder: (context, state) => const PrivacyPolicyScreen(),
      ),
      GoRoute(
        path: PagePath.introToBuyerBoard,
        builder: (context, state) => const AppOnboardingScreen(),
      ),
      GoRoute(
        path: PagePath.help,
        builder: (context, state) => const HelpScreen(),
      ),
      GoRoute(
        path: PagePath.topic,
        builder: (context, state) =>
            TopicScreen(help: state.extra as HelpResponse),
      ),
      GoRoute(
        path: PagePath.faq,
        builder: (context, state) => const FaqScreen(),
      ),
      GoRoute(
        path: PagePath.mainScreen,
        builder: (context, state) => const MainScreen(),
        routes: [
          GoRoute(
            path: 'filters',
            builder: (context, state) => const NewFilterScreen(),
          ),
          GoRoute(
            path: PagePath.expandedBuyerCard.pathLocation,
            builder: (context, state) => ExpandedBuyerCard(
              buyer: state.extra as BuyerModel,
            ),
            routes: [
              GoRoute(
                path: PagePath.editBuyer.pathLocation,
                builder: (context, state) => AddBuyerScreen(
                  buyer: state.extra as BuyerModel?,
                  isEditMode: true,
                ),
              ),
            ],
          ),
        ],
      ),
      GoRoute(
        path: PagePath.chatMainScreen,
        builder: (context, state) {
          return const ChatMainScreen();
        },
        routes: [
          GoRoute(
            path: PagePath.chatDetailsScreen,
            name: PagePath.chatDetailsScreen,
            pageBuilder: (context, state) {
              final record = state.extra as ({
                ChatGroupThreadModel agent,
                ChatGroupModel buyer
              });
              return MaterialPage(
                fullscreenDialog: true,
                child: ChatDetailsScreen(
                  buyer: record.buyer,
                  agent: record.agent,
                ),
              );
            },
          ),
          GoRoute(
            path: PagePath.attachmentDetailsScreen,
            name: PagePath.attachmentDetailsScreen,
            pageBuilder: (context, state) {
              final Map<String, dynamic> record =
                  state.extra as Map<String, dynamic>;
              return MaterialPage(
                fullscreenDialog: true,
                child: SendAttachmentScreen(
                  imagePaths: record['imagePaths'] as List<String>,
                  userId: record['userId'] as int,
                  otherUserId: record['otherUserId'] as int,
                  buyerId: record['buyerId'] as int,
                  stream: record['stream'] as Stream<ChatMessage?>,
                  editStream: record['editStream'] as Stream<ChatMessage?>,
                  onCancelReply: record['onCancelReply'] as VoidCallback,
                  parentMessageId: record['parentMessageId'] as int?,
                  editMessageId: record['editMessageId'] as int?,
                  isMessageEmpty: record['isMessageEmpty'] as bool,
                ),
              );
            },
          ),
          GoRoute(
            path: PagePath.attachmentDetails,
            name: PagePath.attachmentDetails,
            pageBuilder: (context, state) {
              final record = state.extra as Map<String, dynamic>;
              return MaterialPage(
                fullscreenDialog: true,
                child: AttachmentDetails(
                  imagePaths: record['imagePaths'] as List<String>,
                  messages: record['messages'] as List<ChatMessage>,
                  userId: record['userId'] as int,
                  otherUserId: record['otherUserId'] as int,
                  buyerId: record['buyerId'] as int,
                ),
              );
            },
          ),
        ],
      ),
    ],
  );
}
