import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/core/network/http_client.dart';
import 'package:buyer_board/core/network/network_info.dart';
import 'package:buyer_board/core/network/rest_api_client.dart';
import 'package:buyer_board/core/network/service_config.dart';
import 'package:buyer_board/core/resources/constants.dart';
import 'package:dio/dio.dart';
import 'package:kiwi/kiwi.dart';

abstract class NetworkModule {
  static late KiwiContainer _container;

  static void setup(
      {required KiwiContainer container, required AppConfig appConfig}) {
    _container = container;
    _setupServiceConfig(appConfig);
    _setupDioClient();
    _setupRestClient();
    _setupNetworkInfo();
  }

  static void _setupServiceConfig(AppConfig appConfig) {
    _container.registerSingleton<ServiceConfig>(
      (_) => ServiceConfig(
        BaseOptions(
          baseUrl: appConfig.baseUrl,
          connectTimeout: Constants.connectTimeout,
          receiveTimeout: Constants.receiveTimeout,
        ),
      ),
    );
  }

  static void _setupDioClient() {
    _container.registerSingleton<DioHttpClient>(
      (_) => DioHttpClient(
        _container.resolve(),
      ),
    );
  }

  static void _setupRestClient() {
    _container.registerSingleton<RestAPIClient>(
      (_) => RestAPIClient(_container.resolve<DioHttpClient>().client),
    );
  }

  static void _setupNetworkInfo() {
    _container.registerSingleton<NetworkInfo>(
      (_) => NetworkInfoImpl(),
    );
  }
}
