import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/repositories/buyer_location_filter_repository.dart';

class SearchLocationsByZipCodeUseCase {
  final BuyerLocationFilterRepository repository;

  SearchLocationsByZipCodeUseCase(this.repository);

  Future<List<LocationEntity>> call(String search) async {
    return await repository.searchLocationsByZipCode(search: search);
  }
}
