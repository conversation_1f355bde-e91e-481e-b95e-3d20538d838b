import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:flutter/material.dart';

import '../../../../common/widgets/common_radio_tile.dart';
import '../../../../core/resources/resources.dart';
import '../cubit/buyer_info_cubit.dart';

class PropertyTypeWidget extends StatefulWidget {
  const PropertyTypeWidget({super.key});

  @override
  State<PropertyTypeWidget> createState() => _PropertyTypeWidgetState();
}

class _PropertyTypeWidgetState extends State<PropertyTypeWidget> {
  void toglePurchaseType(PropertyType? val) {
    PropertyType? type = propertyTypeNotifier.value;
    type != val
        ? propertyTypeNotifier.value = val!
        : propertyTypeNotifier.value = null;
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: propertyTypeNotifier,
        builder: (context, propertyType, _) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                Strings.propertyType,
                style: context.typography.smallReg.copyWith(
                  color: context.appColors.greyM,
                ),
              ),
              const SizedBox(height: 16),
              CommonRadioTile<PropertyType?>(
                label: PropertyType.singleFamily.label,
                onChange: toglePurchaseType,
                value: PropertyType.singleFamily,
                groupValue: propertyType,
              ),
              const SizedBox(height: 2),
              CommonRadioTile<PropertyType?>(
                label: PropertyType.townhouse.label,
                onChange: toglePurchaseType,
                value: PropertyType.townhouse,
                groupValue: propertyType,
              ),
              const SizedBox(height: 2),
              CommonRadioTile<PropertyType?>(
                label: PropertyType.condo.label,
                onChange: toglePurchaseType,
                value: PropertyType.condo,
                groupValue: propertyType,
              ),
              const SizedBox(height: 2),
              CommonRadioTile<PropertyType?>(
                label: PropertyType.apartment.label,
                onChange: toglePurchaseType,
                value: PropertyType.apartment,
                groupValue: propertyType,
              ),
              const SizedBox(height: 2),
              CommonRadioTile<PropertyType?>(
                label: PropertyType.multiFamily.label,
                onChange: toglePurchaseType,
                value: PropertyType.multiFamily,
                groupValue: propertyType,
              ),
              const SizedBox(height: 2),
              CommonRadioTile<PropertyType?>(
                label: PropertyType.land.label,
                onChange: toglePurchaseType,
                value: PropertyType.land,
                groupValue: propertyType,
              ),
              const SizedBox(height: 2),
              ValueListenableBuilder(
                  valueListenable: propertyTypeError,
                  builder: (context, financialStatus, _) {
                    return propertyTypeError.value!.isNotEmpty
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 2),
                              Container(
                                height: 1,
                                width: double.infinity,
                                color: AppColors.errorMedium,
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 4.0),
                                child: Text(
                                  "Field Required",
                                  style: AppStyles.small.copyWith(
                                      color: AppColors.errorMedium,
                                      fontSize: 11,
                                      fontWeight: FontWeight.w700),
                                ),
                              ),
                            ],
                          )
                        : const SizedBox();
                  })
            ],
          );
        });
  }
}
