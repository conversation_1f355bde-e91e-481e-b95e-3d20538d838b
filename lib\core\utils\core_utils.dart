import 'dart:ui';

import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

abstract final class CoreUtils {
  static String? Function(String?)? emailValidator({bool isRequired = true}) {
    final RegExp emailRegExp = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return (String? value) {
      if (value == null || value.isEmpty) {
        return !isRequired ? null : "Email is required";
      }
      if (!emailRegExp.hasMatch(value)) {
        return "Invalid email";
      }
      if (!value.endsWith('.com')) {
        return "Email must end with '.com'";
      }
      if (value.substring(value.lastIndexOf('.com') + 4).isNotEmpty) {
        return "Email cannot contain text after '.com'";
      }
      return null;
    };
  }

  static String? Function(String?)? fieldRequiredValidator({String? error}) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return error ?? "Field is required";
      }
      return null;
    };
  }

  static String? Function(String?)? zipCodeFormatter({String? error}) {
    // Regular expression for US zip code validation
    final regex = RegExp(r'^\d{5}(-\d{4})?$');

    return (String? value) {
      if (value == null || value.isEmpty) {
        return error ?? "Zip code is required";
      }

      if (!regex.hasMatch(value)) {
        return "Invalid zip code";
      }

      return null;
    };
  }

  static void launchUri({
    required BuildContext context,
    required Uri uri,
  }) async {
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: context.appColors.error,
            content: const Text("Could not launch URL"),
          ),
        );
      }
    }
  }

  static void launchEmailClient({
    required BuildContext context,
    String toEmail = '<EMAIL>',
    String subject = 'BuyerBoard - Help or Feedback',
  }) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: toEmail,
      query: 'subject=$subject',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else if (context.mounted) {
      showSnackBar(context, 'Could not launch email client');
    }
  }

  static void showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: context.appColors.error,
        content: Text(message),
      ),
    );
  }

  static String formatLargeNumber(num squareFootage) {
    if (squareFootage < 1000) {
      return _formatRoundedValue(squareFootage.toDouble());
    } else if (squareFootage < 1000000) {
      double valueInThousands = squareFootage / 1000;
      final result = double.parse(_formatRoundedValue(valueInThousands));
      if (result < 999.995) {
        return '${_formatRoundedValue(valueInThousands)}K';
      } else {
        return '1M';
      }
    } else {
      double valueInMillions = squareFootage / 1000000;

      return '${_formatRoundedValue(valueInMillions)}M';
    }
  }

  static String _formatRoundedValue(double value) {
    // Round to two decimal places
    double roundedValue = (value * 100).round() / 100;

    // Convert to string with fixed decimal places
    String stringValue = roundedValue.toStringAsFixed(2);

    // Remove trailing .00 or .0
    if (stringValue.endsWith('.00')) {
      return stringValue.substring(0, stringValue.length - 3);
    } else if (stringValue.endsWith('0')) {
      return stringValue.substring(0, stringValue.length - 1);
    }

    return stringValue;
  }

  static String? Function(String?)? phoneNumberValidator({
    bool isRequired = true,
  }) {
    // Regex for validating a US phone number with country code +1
    final RegExp phoneNumberRegExp = RegExp(
      r'^\+1\s?\(\d{3}\)\s?\d{3}-\d{4}$',
    );

    return (String? value) {
      if (value == null || value.isEmpty) {
        return !isRequired ? null : 'Phone number is required';
      }

      if (!phoneNumberRegExp.hasMatch(value)) {
        return "Invalid phone number";
      }

      return null;
    };
  }

  static String? Function(String?)? brokerageLicenseNumber({
    bool isRequired = true,
  }) {
    // Combined regular expression for various brokerage license formats
    final RegExp combinedLicenseRegExp = RegExp(
        r'^(?:[0-9]{7,9}|' // US format: 7 to 9 digits
        r'\d{6}|' // UK format: exactly 6 digits
        r'[A-Z]{2}\d{6}|' // Canada format: 2 letters followed by 6 digits
        r'\d{9}|' // Australia format: exactly 9 digits
        r'\d{4}-\d{4})$' // UAE format: 4 digits followed by a hyphen and 4 digits
        );

    return (String? value) {
      if (value == null || value.isEmpty) {
        return !isRequired ? null : 'Brokerage license number is required';
      }

      if (!combinedLicenseRegExp.hasMatch(value)) {
        return 'Invalid brokerage license number';
      }
      return null;
    };
  }

  static String? Function(String?)? agentLicenseNumber(
      {bool isRequired = true}) {
    final RegExp combinedLicenseRegExp = RegExp(
        r'^(?:[A-Z0-9]{6,10}|' // General alphanumeric format: 6 to 10 characters
        r'[A-Z]{2}\d{6}[A-Z]?|' // US format example: 2 letters, 6 digits, optional letter
        r'\d{3}-\d{4}-[A-Z]{2}|' // US example with dashes: 3 digits, 4 digits, 2 letters
        r'[A-Z]{2}\d{4}-\d{4}|' // UK/Canada example: 2 letters, 4 digits, hyphen, 4 digits
        r'\d{4}-[A-Z]{2}\d{4}|' // Australia example: 4 digits, hyphen, 2 letters, 4 digits
        r'[A-Z]{2}-\d{4})$' // UAE example: 2 letters, hyphen, 4 digits
        );

    return (String? value) {
      if (value == null || value.isEmpty) {
        return !isRequired ? null : 'Agent license number is required';
      }

      if (!combinedLicenseRegExp.hasMatch(value)) {
        return 'Invalid agent license number';
      }
      return null;
    };
  }

  static String formatCurrency(double amount,
      {int decimalPlaces = 0, String? symbol}) {
    final NumberFormat currencyFormatter = NumberFormat.currency(
      locale: 'en_US', // US locale for dollar formatting
      symbol: symbol ?? '\$', // Dollar symbol
      decimalDigits: decimalPlaces, // Set the number of decimal places
    );
    return currencyFormatter.format(amount);
  }

  // static String formatBudgetAmount(double amount) {
  //   if (amount >= 1000000) {
  //     double millions = amount / 1000000;
  //     if (millions >= 10) {
  //       return '\$${millions.round()}M';
  //     } else {
  //       String formatted = millions.toStringAsFixed(2);
  //       if (formatted.endsWith('0')) {
  //         formatted = formatted.substring(0, formatted.length - 1);
  //         if (formatted.endsWith('.0')) {
  //           formatted = formatted.substring(0, formatted.length - 2);
  //         }
  //       }
  //       return '\$${formatted}M';
  //     }
  //   } else if (amount >= 1000) {
  //     double thousands = amount / 1000;
  //     if (thousands >= 100) {
  //       return '\$${thousands.round()}K';
  //     } else {
  //       String formatted = thousands.toStringAsFixed(2);
  //       if (formatted.endsWith('0')) {
  //         formatted = formatted.substring(0, formatted.length - 1);
  //         if (formatted.endsWith('.0')) {
  //           formatted = formatted.substring(0, formatted.length - 2);
  //         }
  //       }
  //       return '\$${formatted}K';
  //     }
  //   } else {
  //     return '\$${amount.toStringAsFixed(0)}';
  //   }
  // }

  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String content,
    required String action,
    Color? actionColor,
  }) {
    return showCupertinoDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text(title),
          content: Text(content),
          actions: [
            CupertinoDialogAction(
              isDefaultAction: true,
              onPressed: () {
                Navigator.of(context)
                    .pop(false); // Return false, indicating cancel
              },
              child: const Text("Cancel"),
            ),
            CupertinoDialogAction(
              textStyle: context.typography.mediumSemi.copyWith(
                color: actionColor ?? context.theme.appColors.error,
              ),
              isDestructiveAction: true,
              onPressed: () {
                Navigator.of(context)
                    .pop(true); // Return true, indicating confirm
              },
              child: Text(action),
            ),
          ],
        );
      },
    ).then(
        (value) => value ?? false); // Ensure the future always returns a bool
  }

  static void showCustomToast(BuildContext context,
      {required String title, required String subTitle}) {
    final overlay = Overlay.of(context);

    // Declare the OverlayEntry variable
    late OverlayEntry overlayEntry;

    // Create a function to build the toast and manage the OverlayEntry
    void buildToast() {
      overlayEntry = OverlayEntry(
        canSizeOverlay: true,
        builder: (context) {
          return Positioned(
            top: 50.0, // Adjust this value to position the toast as desired
            width: MediaQuery.of(context).size.width,
            child: _CustomToast(
              onDismiss: () {
                // Use the overlayEntry variable here
                overlayEntry.remove();
              },
              title: title,
              subTitle: subTitle,
            ),
          );
        },
      );

      overlay.insert(overlayEntry);

      // Remove the toast after the specified duration
      Future.delayed(const Duration(seconds: 12), () {
        if (overlayEntry.mounted) {
          overlayEntry.remove();
        }
      });
    }

    // Build and show the toast
    buildToast();
  }
}

class _CustomToast extends StatelessWidget {
  final String title;
  final String? subTitle;
  final VoidCallback onDismiss;

  const _CustomToast({
    Key? key,
    required this.title,
    this.subTitle,
    required this.onDismiss,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Material(
        type: MaterialType.transparency,
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  height: 85,
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                      horizontal: 24.0, vertical: 12.0),
                  decoration: BoxDecoration(
                    color: context.theme.appColors.whiteBlack.withOpacity(.8),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: context.theme.appColors.black.withOpacity(.2),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (title.isNotEmpty)
                        Text(
                          title,
                          style: context.typography.largeBlack,
                        ),
                      if (subTitle!.isNotEmpty) const SizedBox(height: 8),
                      if (subTitle!.isNotEmpty)
                        Text(
                          subTitle!,
                          style: context.typography.mediumReg,
                        ),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              right: 0,
              top: 0,
              child: IconButton(
                onPressed: onDismiss,
                icon: Icon(
                  Icons.cancel,
                  size: 28,
                  color: context.appColors.blackWhite,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
