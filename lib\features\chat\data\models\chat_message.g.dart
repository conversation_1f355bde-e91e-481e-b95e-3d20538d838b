// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatMessage _$ChatMessageFromJson(Map<String, dynamic> json) => ChatMessage(
      id: (json['id'] as num?)?.toInt(),
      message: json['content'] as String? ?? '',
      sentBy: (json['sent_by'] as num?)?.toInt(),
      timestamp: json['initiated_at'] as String,
      status: $enumDecodeNullable(_$ChatMessageStatusEnumMap, json['status']) ??
          ChatMessageStatus.sent,
      isDeleted: json['is_deleted'] as bool? ?? false,
      parentId: (json['parent_id'] as num?)?.toInt(),
      parentMessage: json['parent_message'] == null
          ? null
          : ChatMessage.fromJson(
              json['parent_message'] as Map<String, dynamic>),
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) =>
                  ChatMessageAttachment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ChatMessageToJson(ChatMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'content': instance.message,
      'initiated_at': instance.timestamp,
      'sent_by': instance.sentBy,
      'status': _$ChatMessageStatusEnumMap[instance.status]!,
      'is_deleted': instance.isDeleted,
      'parent_id': instance.parentId,
      'parent_message': instance.parentMessage?.toJson(),
      'attachments': instance.attachments.map((e) => e.toJson()).toList(),
    };

const _$ChatMessageStatusEnumMap = {
  ChatMessageStatus.sending: 'sending',
  ChatMessageStatus.sent: 'sent',
  ChatMessageStatus.failed: 'failed',
};

ChatMessageAttachment _$ChatMessageAttachmentFromJson(
        Map<String, dynamic> json) =>
    ChatMessageAttachment(
      type: json['type'] as String,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnail_url'],
    );

Map<String, dynamic> _$ChatMessageAttachmentToJson(
        ChatMessageAttachment instance) =>
    <String, dynamic>{
      'type': instance.type,
      'url': instance.url,
      'thumbnail_url':instance.thumbnailUrl
    };
