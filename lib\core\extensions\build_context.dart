import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/theme/app_color_extension.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/core/theme/app_typography_extension.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../common/widgets/toast_widget.dart';
import '../resources/resources.dart';

extension ContextExtensions on BuildContext {
  double get bottomInsets => MediaQuery.of(this).viewInsets.bottom;
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => Theme.of(this).textTheme;
  ColorScheme get colorScheme => Theme.of(this).colorScheme;
  AppTypographyExtension get typography => Theme.of(this).appTypography;
  AppColorsExtension get appColors => Theme.of(this).appColors;
  double get deviceHeight => MediaQuery.of(this).size.height;
  double get deviceWidth => MediaQuery.of(this).size.width;
  void showToast(
      {String? message,
      int? duration,
      bool isSuccess = true,
      Widget? messageWidget}) {
    if (message == null || message.isEmpty) {
      return;
    }
    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        if (!mounted) return;
        Toast.show(
            context: this,
            duration: duration,
            widget: ConstrainedBox(
              constraints: BoxConstraints(
                minWidth: MediaQuery.sizeOf(this).width - 24,
                // maxHeight: 114,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: isSuccess ? AppColors.success : AppColors.error,
                  borderRadius: BorderRadius.circular(4),
                ),
                // width: double.infinity,
                padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.padding_8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 16),
                    Text(
                      isSuccess ? 'Success!' : 'Error Detected!',
                      style: AppStyles.largeBold2x
                          .copyWith(color: AppColors.white),
                    ),
                    // const SizedBox(height: 16),
                    Text(message,
                        style: textTheme.labelLarge?.copyWith(
                            fontWeight: FontWeight.w400,
                            color: AppColors.white)),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
            gravity: ToastGravity.top);
      },
    );
  }

  void showCustomToast(
      {String? message,
      int? duration,
      bool isSuccess = true,
      String? actionButtonText,
      Widget? messageWidget,
      TextStyle? style,
      required Null Function() onPressed}) {
    if (message == null || message.isEmpty) {
      return;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      Toast.show(
        context: this,
        duration: duration,
        widget: Padding(
          padding: const EdgeInsets.all(Dimensions.materialPadding),
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              minHeight: 57,
              minWidth: double.infinity,
            ),
            child: Container(
              decoration: BoxDecoration(
                color: isSuccess ? AppColors.success : AppColors.error,
                borderRadius: BorderRadius.circular(4),
              ),
              padding: const EdgeInsets.all(Dimensions.padding_8),
              child: Row(
                children: [
                  // Icon(
                  //   Icons.favorite,
                  //   // color: AppColors.appBarRedColor
                  // ),
                  // const SizedBox(width: 8),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        message,
                        style: textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.w400,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                  InkWell(
                      onTap: onPressed,
                      child: Text(
                        actionButtonText!,
                        style: style!,
                      )),
                  const SizedBox(
                    width: Dimensions.padding_10,
                  )
                ],
              ),
            ),
          ),
        ),
        gravity: ToastGravity.top,
      );
    });
  }

  void showSnack(
      {required String message,
      Duration? duration,
      bool isFailure = true,
      SnackBarAction? action}) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(this).showSnackBar(
        SnackBar(
          backgroundColor: isFailure ? AppColors.error : AppColors.success,
          content: Text(
            message,
            style: textTheme.titleSmall?.copyWith(
              color: AppColors.white,
            ),
          ),
          action: action,
          duration: duration ?? const Duration(seconds: 4),
        ),
      );
    });
  }

  // Use this instead of context.pop()
  void shouldPop() {
    if (canPop()) {
      pop();
    }
  }
}
