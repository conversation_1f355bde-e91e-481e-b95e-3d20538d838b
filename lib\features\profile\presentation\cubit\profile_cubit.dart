import 'package:buyer_board/common/utils/image_utils.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:buyer_board/features/profile/data/models/requests/update_profile_request.dart';
import 'package:buyer_board/features/profile/domain/repository/user_repository.dart';
import 'package:buyer_board/features/profile/presentation/states/profile_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/network/interceptors/error_handler_interceptor.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit({
    required this.userRepository,
    required this.appPreferences,
    required this.userSession,
  }) : super(const ProfileState.initial());
  final UserRepository userRepository;
  final AppPreferences appPreferences;
  final UserSessionCubit userSession;
  String? selectedImagePath;
  Future<String?> selectProfileImage() async {
    selectedImagePath = await ImageUtils().picAndCropImageFromGallery();
    return selectedImagePath;
  }

  User? getUserProfile() {
    final userProfile = appPreferences.getUser() ?? userSession.state;
    if (userProfile != null) {
      emit(ProfileState.success(user: userProfile));
    }
    return userProfile;
  }

  Future<void> updateUserProfile({
    String? email,
    String? firstName,
    String? lastName,
    String? primaryPhoneNumber,
    String? optionalPhoneNumber,
    String? brokerageName,
    String? brokerageLisenceNo,
    String? brokerageStreetAddress,
    String? brokerageCity,
    String? brokerageZipCode,
    String? brokerageState,
    String? agentLicenseIdNo,
  }) async {
    emit(const ProfileState.loading());
    String? uploadedUrl;
    if (selectedImagePath != null) {
      uploadedUrl = await ImageUtils().uploadImage(selectedImagePath!);
    }
    try {
      final userProfile = appPreferences.getUser() ?? userSession.state;
      final updatedProfile = await userRepository.updateUserProfile(
        updateProfileRequest: UpdateProfileRequest(
          email: email,
          firstName: firstName,
          lastName: lastName,
          primaryPhoneNumber: primaryPhoneNumber,
          brokerageName: brokerageName,
          brokerageLisenceNo: brokerageLisenceNo,
          brokerageStreetAddress: brokerageStreetAddress,
          brokerageCity: brokerageCity,
          brokerageZipCode: brokerageZipCode,
          brokerageState: brokerageState,
          agentLicenseIdNo: agentLicenseIdNo,
          imageUrl: uploadedUrl,
        ),
      );
      if (updatedProfile.data != null) {
        final profileData = appPreferences.getUser() ?? userSession.state;
        final updateUser = profileData?.copyWith(
          profile: updatedProfile.data,
          token: userProfile?.token ?? "",
        );
        if (appPreferences.getUser() != null) {
          appPreferences.setUser(updateUser!);
        } else {
          userSession.setUser(updateUser!);
        }
        emit(ProfileState.success(
          user: updateUser,
          message: updatedProfile.message.body ?? "",
        ));
      }
    } on ErrorObjectException catch (e) {
      String errorMessage;
      try {
        final errorData = e.errorObject;
        if (errorData != null && errorData.isNotEmpty) {
          final firstErrorKey = errorData.keys.first;
          final firstErrorMessage = errorData[firstErrorKey];
          if (firstErrorMessage is String) {
            errorMessage = firstErrorMessage;
          } else {
            errorMessage = "An unexpected error occurred.";
          }
        } else {
          errorMessage = "An unexpected error occurred.";
        }
      } catch (_) {
        errorMessage =
            "An error occurred. Please check your input and try again.";
      }
      emit(ProfileState.profileError(errorMessage));
    } catch (e) {
      emit(const ProfileState.profileError(
          "An unknown error occurred. Please try again."));
    }
  }
}
