import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;

class LocalAuthUtil {
  final _localAuth = LocalAuthentication();
  Future<bool> canAuthenticate() async {
    return await _localAuth.canCheckBiometrics ||
        await _localAuth.isDeviceSupported();
  }

  Future<List<BiometricType>> getAvailableLocalAuthMethods() async {
    return await _localAuth.getAvailableBiometrics();
  }

  Future<bool> authenticate() async {
    bool didAuthenticate = false;
    try {
      didAuthenticate = await _localAuth.authenticate(
          localizedReason: 'Authentication Required!',
          options: const AuthenticationOptions(
            biometricOnly: true,
            stickyAuth: true,
          ));
    } on PlatformException catch (e) {
      debugPrint(e.toString());
      if (e.code == auth_error.notEnrolled) {
        debugPrint(e.code);
      } else if (e.code == auth_error.lockedOut ||
          e.code == auth_error.permanentlyLockedOut) {
        debugPrint(e.code);
      } else {
        debugPrint(e.code);
      }
      rethrow;
    }
    return didAuthenticate;
  }
}
