import 'dart:io';
import 'package:buyer_board/core/network/end_points.dart';
import 'package:buyer_board/core/network/base_response.dart';
import 'package:buyer_board/core/network/network_keys.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/auth/data/models/requests/apple_login_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/auth_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/signup_auth_request.dart';
import 'package:buyer_board/features/auth/data/models/requests/social_auth_request.dart';
import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:buyer_board/features/buyers_location_filter/data/models/location_model.dart';
import 'package:buyer_board/features/chat/data/models/upload_attachments_model.dart';
import 'package:buyer_board/features/filters/models/new_filter_model.dart';
import 'package:buyer_board/features/forget_password/data/models/reset_password_request.dart';
import 'package:buyer_board/features/profile/data/models/requests/update_profile_request.dart';
import 'package:buyer_board/features/profile/data/models/response/upload_image_response.dart';
import 'package:buyer_board/features/profile/data/models/response/user_address_response.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../../features/forget_password/data/models/verify_otp_request.dart';
part 'rest_api_client.g.dart';

@RestApi()
abstract class RestAPIClient {
  factory RestAPIClient(Dio dio, {String baseUrl}) = _RestAPIClient;
  @POST(Endpoints.login)
  Future<BaseResponse<User>> loginWithEmailPassword({
    @Body() required AuthRequest loginRequest,
  });
  @POST(Endpoints.register)
  Future<BaseResponse<User>> signUpWithEmailPassword({
    @Body() required SignUpAuthRequest signupRequest,
  });
  @POST(Endpoints.appleLogin)
  Future<BaseResponse<User>> loginWithApple({
    @Body() required AppleLoginRequest appleLoginRequest,
  });
  @POST(Endpoints.socialAuth)
  Future<BaseResponse<User>> socialAuth({
    @Body() required SocialAuthRequest socialAuthRequest,
  });
  @POST(Endpoints.resetPassword)
  Future<BaseResponse> forgetPassword({
    @Body() required ResetPasswordRequest resetPasswordRequest,
  });
  @POST(Endpoints.verifyOtp)
  Future<BaseResponse> verifyOtp({
    @Body() required VerifyOtpRequest verifyOtpRequest,
  });
  @PUT(Endpoints.resetPassword)
  Future<BaseResponse> updatePassword({
    @Body() required AuthRequest authRequest,
  });

  @POST(Endpoints.uploadImage)
  @MultiPart()
  Future<BaseResponse<UploadImage>> uploadImage({
    @Part() File? image,
  });

  @POST(Endpoints.uploadAttachment)
  @MultiPart()
  Future<BaseResponse<List<UploadAttachment?>>> uploadAttachment({
    @Part() required File files,
  });

  @PUT(Endpoints.profile)
  Future<BaseResponse<UserProfile>> updateUserProfile({
    @Body() required UpdateProfileRequest updateProfileRequest,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @POST(Endpoints.buyer)
  Future<BaseResponse<BuyerModel>> addBuyer({
    @Body() required BuyerModel buyer,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @GET(Endpoints.getAddressFromZipCode)
  Future<BaseResponse<UserAddress>> getAddressFromZipCode({
    @Path("zipCode") required String zipCode,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });
  @PUT(Endpoints.editBuyer)
  Future<BaseResponse<BuyerModel>> editBuyer({
    @Body() required BuyerModel buyer,
    @Path("id") required int id,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @GET(Endpoints.getBuyer)
  Future<BaseResponse<BuyerModel>> getBuyer({
    @Path("id") required int id,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @DELETE(Endpoints.logOut)
  Future<dynamic> logOut({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });
  @GET(Endpoints.getAllBuyers)
  Future<BaseResponse<List<BuyerModel>>> getAllBuyers({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });
  @GET(Endpoints.getMyBuyers)
  Future<BaseResponse<List<BuyerModel>>> getMyBuyers({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });
  @GET(Endpoints.getOtherBuyers)
  Future<BaseResponse<List<BuyerModel>>> getOtherBuyers({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });
  @GET(Endpoints.getFavouriteBuyers)
  Future<BaseResponse<List<BuyerModel>>> getFavouriteBuyers({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });
  @POST(Endpoints.favouriteBuyer)
  Future<BaseResponse<BuyerModel>> favouriteBuyer({
    @Body() required Map<String, dynamic> isFavourite,
    @Path("id") required int id,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @POST(Endpoints.searchBuyers)
  Future<BaseResponse<List<BuyerModel>>> getBuyersByZipCode({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
    @Path("zipCode") required String zipCode,
  });

  @POST(Endpoints.filteredBuyers)
  Future<BuyerModelResponse> filteredBuyers({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
    @Body() required NewFilterModel filterModel,
  });

  @POST(Endpoints.updateNote)
  Future<BaseResponse<BuyerModel>> updateNote({
    @Path("buyerId") required int id,
    @Field() required String content,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @POST(Endpoints.buyersLocation)
  Future<BaseResponse<List<LocationModel>>> getFilterLocations({
    @Field("zip_code") String? zipCode,
    @Field("page") required int page,
    @Field("page_size") required int pageSize,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @POST(Endpoints.buyersLocation)
  Future<BaseResponse<List<LocationModel>>> getSearchLocations({
    @Field("zip_code") String? search,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @DELETE(Endpoints.deleteUser)
  Future<dynamic> deleteUser({
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });

  @POST(Endpoints.deleteAttachment)
  Future<String> deleteAttachments({
    @Body() required List<Map<String, dynamic>> attachment,
    @Header(NetworkKeys.requiresToken) requiresToken = true,
  });
}
