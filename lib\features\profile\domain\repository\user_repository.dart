import 'dart:io';

import 'package:buyer_board/core/network/base_response.dart';
import 'package:buyer_board/features/profile/data/models/response/upload_image_response.dart';
import 'package:buyer_board/features/profile/data/models/response/user_address_response.dart';

import '../../../auth/data/models/response/auth_response.dart';
import '../../data/models/requests/update_profile_request.dart';

abstract class UserRepository {
  Future<BaseResponse<UserProfile>> updateUserProfile(
      {required UpdateProfileRequest updateProfileRequest});

  Future<UploadImage?> uploadProfileImage(
      {required File profileImage});
  Future<BaseResponse<UserAddress>> getAddressFromZipCode(
      {required String zipCode});
}
