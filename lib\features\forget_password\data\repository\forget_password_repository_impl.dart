import 'package:buyer_board/core/network/rest_api_client.dart';

import '../../../../core/network/base_response.dart';
import '../../../auth/data/models/requests/auth_request.dart';
import '../../domain/repository/forget_password_repository.dart';
import '../models/reset_password_request.dart';
import '../models/verify_otp_request.dart';

class ResetPasswordRepositoryImpl extends ResetPasswordRepository {
  ResetPasswordRepositoryImpl({required this.restAPIClient});
  final RestAPIClient restAPIClient;
  @override
  Future<BaseResponse> forgetPassword(
      {required ResetPasswordRequest resetPasswordRequest}) async {
    return await restAPIClient.forgetPassword(
        resetPasswordRequest: resetPasswordRequest);
  }

  @override
  Future<BaseResponse> verifyOtp(
      {required VerifyOtpRequest verifyOtpRequest}) async {
    return await restAPIClient.verifyOtp(verifyOtpRequest: verifyOtpRequest);
  }

  @override
  Future<BaseResponse> updatePassword(
      {required AuthRequest authRequest}) async {
    return await restAPIClient.updatePassword(authRequest: authRequest);
  }
}
