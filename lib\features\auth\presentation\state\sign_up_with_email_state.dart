import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'sign_up_with_email_state.freezed.dart';

@freezed
class SignUpWithEmailState with _$SignUpWithEmailState {
  const factory SignUpWithEmailState.initial() = initial;
  const factory SignUpWithEmailState.loading() = loading;
  const factory SignUpWithEmailState.success(
      User authResponse, String message) = success;
  const factory SignUpWithEmailState.rememberMe(String message) = rememberMe;
  const factory SignUpWithEmailState.signupError(String? error) = signupError;
}
