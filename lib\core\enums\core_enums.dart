// import 'package:flutter/material.dart';

// enum AppThemeMode {
//   light,
//   dark,
//   system;

//   String get name {
//     return switch (this) {
//       AppThemeMode.light => 'Light',
//       AppThemeMode.dark => 'Dark',
//       AppThemeMode.system => 'Use device theme',
//     };
//   }

//   ThemeMode get themeValue {
//     return switch (this) {
//       AppThemeMode.light => ThemeMode.light,
//       AppThemeMode.dark => ThemeMode.dark,
//       AppThemeMode.system => ThemeMode.system,
//     };
//   }
// }
