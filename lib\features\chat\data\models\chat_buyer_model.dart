import 'package:buyer_board/features/auth/data/models/response/auth_response.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:json_annotation/json_annotation.dart';

part 'chat_buyer_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class ChatGroupModel {
  ChatGroupModel({
    required this.threads,
    required this.sku,
    required this.myBuyer,
    required this.buyerId,
    required this.id,
    required this.buyersAlias,
    required this.firstName,
    required this.lastName,
  }) : hasNewMessage = threads.any((agent) => agent.hasNewMessage);
  @JsonKey(defaultValue: [])
  final List<ChatGroupThreadModel> threads;
  @JsonKey(defaultValue: '')
  final String? sku;
  @JsonKey(defaultValue: false)
  final bool myBuyer;
  @JsonKey(includeFromJson: false)
  final bool hasNewMessage;
  final int? id;
  final int buyerId;
  final String? buyersAlias;
  final String firstName;
  final String lastName;

  factory ChatGroupModel.fromJson(Map<String, dynamic> json) =>
      _$ChatGroupModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChatGroupModelToJson(this);

  ChatGroupModel copyWith({
    List<ChatGroupThreadModel>? threads,
    String? sku,
    bool? myBuyer,
    int? id,
    int? buyerId,
    String? buyersAlias,
    String? firstName,
    String? lastName,
  }) {
    return ChatGroupModel(
      threads: threads ?? this.threads,
      sku: sku ?? this.sku,
      myBuyer: myBuyer ?? this.myBuyer,
      id: id ?? this.id,
      buyerId: buyerId ?? this.buyerId,
      buyersAlias: buyersAlias ?? this.buyersAlias,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
    );
  }
}

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class ChatGroupThreadModel {
  const ChatGroupThreadModel({
    required this.id,
    required this.agentId,
    required this.avatarUrl,
    required this.name,
    required this.lastMessage,
    required this.timeStamp,
    required this.hasNewMessage,
    required this.userId,
    this.agentImageUrl,
    this.userImageUrl,
    required this.user,
  });

  final int? id;
  final int agentId;
  final String? avatarUrl;
  final String? name;
  @JsonKey(name: 'latest_message')
  final ChatMessage? lastMessage;
  final User? user;
  final String? timeStamp;
  final int userId;
  @JsonKey(defaultValue: false)
  final bool hasNewMessage;
  final String? agentImageUrl;
  final String? userImageUrl;

  factory ChatGroupThreadModel.fromJson(Map<String, dynamic> json) =>
      _$ChatGroupThreadModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChatGroupThreadModelToJson(this);

  ChatGroupThreadModel copyWith({
    int? id,
    int? agentId,
    String? avatarUrl,
    String? name,
    ChatMessage? lastMessage,
    String? timeStamp,
    bool? hasNewMessage,
    int? userId,
    String? agentImageUrl,
    String? userImageUrl,
    User? user,
  }) {
    return ChatGroupThreadModel(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      name: name ?? this.name,
      lastMessage: lastMessage ?? this.lastMessage,
      timeStamp: timeStamp ?? this.timeStamp,
      hasNewMessage: hasNewMessage ?? this.hasNewMessage,
      userId: userId ?? this.userId,
      agentImageUrl: agentImageUrl ?? this.agentImageUrl,
      userImageUrl: userImageUrl ?? this.userImageUrl,
      user: user ?? this.user,
    );
  }
}
