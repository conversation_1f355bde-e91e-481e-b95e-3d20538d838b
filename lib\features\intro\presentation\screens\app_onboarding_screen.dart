import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/drawables.dart';
import 'package:buyer_board/features/home/<USER>/cubit/home_tab_cubit.dart';
import 'package:buyer_board/features/intro/presentation/bloc/intro_hydrated_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class AppOnboardingScreen extends StatefulWidget {
  const AppOnboardingScreen({
    super.key,
    this.child,
    this.onSkip,
    this.onDone,
  });

  final Widget? child;
  final VoidCallback? onSkip;
  final void Function(bool shouldCreateCard)? onDone;

  @override
  State<AppOnboardingScreen> createState() => _AppOnboardingScreenState();
}

class _AppOnboardingScreenState extends State<AppOnboardingScreen> {
  late PageController _pageController;
  double _currentPage = 0.0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController()
      ..addListener(() {
        setState(() {
          _currentPage = _pageController.page ?? 0.0;
        });
      });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IntroHydratedCubit, bool>(
      builder: (context, shown) {
        if (shown && widget.child != null) {
          return widget.child!;
        }
        return Scaffold(
          backgroundColor: context.appColors.pPXLight,
          body: Stack(
            alignment: Alignment.center,
            children: [
              Positioned(
                bottom: 0,
                left: 0,
                child: IgnorePointer(
                  child: Opacity(
                    opacity: 0.1,
                    child: Image.asset(
                      Drawables.buyerBoardLogoHalf,
                      height: MediaQuery.of(context).size.height * .35,
                    ),
                  ),
                ),
              ),
              Positioned(
                top: MediaQuery.of(context).size.height * .32 + 28,
                child: IgnorePointer(
                  child: SmoothPageIndicator(
                    controller: _pageController,
                    count: 5,
                    effect: ScrollingDotsEffect(
                      activeDotScale: 1,
                      dotWidth: 8.0,
                      dotHeight: 8.0,
                      activeDotColor: context.appColors.whitePDark,
                      dotColor: context.appColors.whitePDark.withOpacity(0.3),
                    ),
                  ),
                ),
              ),
              Column(
                children: [
                  Expanded(
                    child: _PageView(
                      pageController: _pageController,
                      currentPage: _currentPage,
                    ),
                  ),
                  if (_currentPage <= 3.0)
                    Container(
                      width: MediaQuery.of(context).size.width,
                      padding: EdgeInsets.only(
                        left: 24,
                        right: 24,
                        bottom: 24 + MediaQuery.of(context).padding.bottom,
                      ),
                      child: OutlinedButton(
                        onPressed: widget.onSkip ??
                            () {
                              context.read<IntroHydratedCubit>().done();
                            },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(
                            color: context.appColors.whitePDark,
                          ),
                        ),
                        child: Text(
                          'Skip',
                          style: context.typography.mediumSemi.copyWith(
                            color: context.appColors.whitePDark,
                          ),
                        ),
                      ),
                    ),
                  if (_currentPage > 3)
                    Opacity(
                      opacity: _currentPage - 3,
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: 24,
                          right: 24,
                          bottom: 24 + MediaQuery.of(context).padding.bottom,
                        ),
                        child: _CreateFirstBuyerCardActions(
                          onAction: widget.onDone ??
                              (shouldCreateCard) {
                                context
                                    .read<HomeBottomNarBarTabCubit>()
                                    .changeTab(shouldCreateCard ? 2 : 0);
                                Future.microtask(() {
                                  context.read<IntroHydratedCubit>().done();
                                });
                              },
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class _PageView extends StatelessWidget {
  const _PageView({
    super.key,
    required this.pageController,
    required this.currentPage,
  });

  final PageController pageController;
  final double currentPage;

  @override
  Widget build(BuildContext context) {
    final content = [
      (
        title: 'Welcome to BuyerBoard',
        subtitle: null,
        desc:
            'The first-ever real estate database created by agents for agents that helps you confidentially match your buyers with off-market properties'
      ),
      (
        title: 'How It Works',
        subtitle: 'Create A BuyerCard',
        desc:
            'Buyers Agents build confidential BuyerCards that identify all the needs of the client'
      ),
      (
        title: 'How It Works',
        subtitle: 'Explore the BuyerBoard',
        desc:
            'Listing Agents review our BuyerCard database by zip code to find potential matches for their off-market properties'
      ),
      (
        title: 'How It Works',
        subtitle: 'Communicate In-App',
        desc:
            'Agents communicate directly in-app about a potential match while client information remains confidential'
      ),
      (
        title: 'Create Your First BuyerCard',
        subtitle: null,
        desc: '…And bring your buyer home'
      ),
    ];
    return SizedBox(
      height: MediaQuery.of(context).size.height,
      child: PageView.builder(
        controller: pageController,
        itemCount: 5,
        itemBuilder: (context, index) {
          double opacity = 1.0;
          if (index == currentPage.floor()) {
            // The current page fading out as it moves away
            opacity = 1 - (currentPage - index);
          } else if (index == currentPage.floor() + 1) {
            // The next page fading in as it moves closer
            opacity = currentPage - index + 1;
          }
          return Opacity(
            opacity: opacity.clamp(0.0, 1.0),
            child: Column(
              children: [
                Image.asset(
                  'assets/images/png/intro_image_${index + 1}.png',
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: MediaQuery.of(context).size.height * .32,
                ),
                const SizedBox(height: 112),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    // mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        content[index].title,
                        textAlign: TextAlign.center,
                        style: context.typography.large1xBlack.copyWith(
                          color: context.appColors.whitePDark,
                        ),
                      ),
                      if (content[index].subtitle != null)
                        Text(
                          content[index].subtitle!,
                          textAlign: TextAlign.center,
                          style: context.typography.largeReg.copyWith(
                            color: context.appColors.whitePDark,
                          ),
                        ),
                      spacerH32,
                      Text(
                        content[index].desc,
                        textAlign: TextAlign.center,
                        style: context.typography.largeReg.copyWith(
                          color: context.appColors.whitePDark,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class _CreateFirstBuyerCardActions extends StatelessWidget {
  const _CreateFirstBuyerCardActions({required this.onAction});

  final void Function(bool shouldCreateCard) onAction;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              onAction(false);
            },
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: context.appColors.whitePDark,
              ),
            ),
            child: Text(
              'Not Now',
              style: context.typography.mediumSemi.copyWith(
                color: context.appColors.whitePDark,
              ),
            ),
          ),
        ),
        spacerW8,
        Expanded(
          child: FilledButton(
            onPressed: () {
              onAction(true);
            },
            style: FilledButton.styleFrom(
              backgroundColor: context.appColors.whitePDark,
              foregroundColor: context.appColors.pPXLight,
            ),
            child: Text(
              'Yes',
              style: context.typography.mediumSemi.copyWith(
                color: context.appColors.pPXLight,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
