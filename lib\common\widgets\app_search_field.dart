import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppSearchField extends StatelessWidget {
  const AppSearchField({
    super.key,
    required this.onChanged,
    this.hintText = 'Search',
  });

  final void Function(String) onChanged;
  final String hintText;

  @override
  Widget build(BuildContext context) {
    final xColors = context.theme.appColors;
    return TextField(
      onChanged: onChanged,
      textInputAction: TextInputAction.done,
      inputFormatters: [
        FilteringTextInputFormatter.deny(RegExp(r'\s')),
      ],
      decoration: InputDecoration(
        fillColor: xColors.whitePXDark,
        filled: true,
        isDense: true,
        prefixIcon: Icon(
          Icons.search_outlined,
          size: 24,
          color: context.colorScheme.primary,
        ),
        hintText: hintText,
        hintStyle: context.typography.mediumReg.copyWith(
          color: xColors.greyLightPDefault,
        ),
        border: _inputBorder(xColors.greyLightPXDark),
        enabledBorder: _inputBorder(xColors.greyLightPXDark),
      ),
    );
  }

  OutlineInputBorder _inputBorder(Color color) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(100),
      borderSide: BorderSide(
        color: color,
        width: 1,
      ),
    );
  }
}
