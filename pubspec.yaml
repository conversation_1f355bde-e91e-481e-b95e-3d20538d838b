name: buyer_board
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.

# Staging version (iOS)
# version: 2.0.3+20

# Staging version (anddoid)
# version: 2.0.3+20

# Production version
version: 2.0.8+35

environment:
  sdk: ">=3.0.5 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:

  cupertino_icons: ^1.0.2
  dio: ^5.4.3+1
  dotted_border: ^2.1.0
  equatable: ^2.0.5
  flutter:
    sdk: flutter
  flutter_animate: ^4.5.0
  flutter_bloc: ^9.0.0
  flutter_easyloading: ^3.0.5
  flutter_multi_formatter: ^2.13.0
  flutter_secure_storage: ^10.0.0-beta.4
  flutter_slidable: ^4.0.0
  flutter_svg: ^2.0.10+1
  freezed_annotation: ^3.0.0
  geocoding: ^3.0.0
  geolocator: ^13.0.2
  get_storage: ^2.1.1
  go_router: ^14.0.2
  google_fonts: ^6.2.1
  google_sign_in: ^6.2.1
  hydrated_bloc: ^10.0.0
  image_cropper: ^9.0.0
  image_picker: ^1.1.2
  internet_connection_checker_plus: ^2.5.2
  intl: ^0.20.2
  json_annotation: ^4.9.0
  kiwi: ^5.0.0
  local_auth: ^2.2.0
  logger: ^2.4.0
  mask_text_input_formatter: ^2.9.0
  onesignal_flutter: ^5.2.9
  package_info_plus: ^8.0.0
  path_provider: ^2.1.3
  permission_handler: ^11.3.1
  pinput: ^5.0.1
  pretty_dio_logger: ^1.3.1
  retrofit: ^4.1.0
  rxdart: ^0.28.0
  sentry_flutter: ^8.9.0
  sign_in_with_apple: ^6.1.1
  smooth_page_indicator: ^1.2.0+3
  url_launcher: ^6.3.0
  web_socket_channel: ^3.0.1
  video_player: ^2.9.2
  file_picker: ^9.0.2
  fluttertoast: ^8.2.8
  firebase_core: ^3.12.1

dev_dependencies:
  build_runner: ^2.4.9
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  freezed: ^3.0.3
  json_serializable: ^6.8.0
  retrofit_generator: ^9.1.7





# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/icons/svg/
    # - assets/icons/png/
    - assets/images/svg/
    - assets/images/png/
    - assets/json/
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Noto Sans
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/NotoSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/NotoSans-Black.ttf
          weight: 900
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
