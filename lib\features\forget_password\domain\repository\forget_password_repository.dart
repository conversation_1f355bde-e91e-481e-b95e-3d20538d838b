import '../../../../core/network/base_response.dart';
import '../../../auth/data/models/requests/auth_request.dart';
import '../../data/models/reset_password_request.dart';
import '../../data/models/verify_otp_request.dart';

abstract class ResetPasswordRepository {
  Future<BaseResponse> forgetPassword(
      {required ResetPasswordRequest resetPasswordRequest});
  Future<BaseResponse> verifyOtp({required VerifyOtpRequest verifyOtpRequest});
  Future<BaseResponse> updatePassword({required AuthRequest authRequest});
}
