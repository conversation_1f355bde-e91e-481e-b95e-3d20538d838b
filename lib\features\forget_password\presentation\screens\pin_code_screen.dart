import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/colors.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/strings.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/features/forget_password/presentation/cubit/reset_passsord_cubit.dart';
import 'package:buyer_board/features/forget_password/presentation/state/reset_password_state.dart';
import 'package:buyer_board/features/splash/presentation/widgets/logo_and_headline_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../widgets/pin_code_fields.dart';

class PinCodeScreen extends StatefulWidget {
  const PinCodeScreen({super.key, required this.email});
  final String email;
  @override
  State<PinCodeScreen> createState() => _PinCodeScreenState();
}

class _PinCodeScreenState extends State<PinCodeScreen> {
  final pinController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  void onSendMeTheCode() {
    context.read<ResetPasswordCubit>().requestOtp(email: widget.email);
  }

  @override
  void dispose() {
    pinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ResetPasswordCubit, ResetPasswordState>(
      listener: (context, state) => state.maybeMap(
          loading: (state) => Loader.show(),
          otpSent: (state) => {
                Loader.hide(),
                context.showToast(isSuccess: true, message: state.message)
              },
          otpVerified: (state) => {
                context
                  ..showToast(isSuccess: true, message: state.message)
                  ..go(PagePath.updatePasswordScreen, extra: widget.email),
              },
          otpVerificationError: (state) => {
                Loader.hide(),
                context.showToast(isSuccess: false, message: state.error),
              },
          orElse: () => Loader.hide()),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: AppColors.primary,
        body: Padding(
          padding: const EdgeInsets.all(24),
          child: SafeArea(
            child: SizedBox(
              width: double.maxFinite,
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const LogoAndHeadlineSection(),
                    const SizedBox(height: 80),
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        Strings.enterCodeHere,
                        style: AppStyles.mediumSemiBold
                            .copyWith(color: AppColors.white),
                      ),
                    ),
                    const SizedBox(height: 16),
                    PinCodeFields(
                      pinController: pinController,
                      onCompleted: (otp) => context
                          .read<ResetPasswordCubit>()
                          .verifyOtp(email: widget.email, otp: otp),
                    ),
                    const Spacer(),
                    Text(
                      Strings.codeNoteReceivedMessage,
                      textAlign: TextAlign.center,
                      style: AppStyles.small.copyWith(color: AppColors.white),
                    ),
                    const SizedBox(height: 16),
                    CommonButton.basic(
                      buttonType: ButtonType.outline,
                      label: Strings.sendMeANewCode,
                      action: () {
                        if (_formKey.currentState?.validate() ?? false) {
                          onSendMeTheCode();
                        }
                      },
                      backgroundColor: context.appColors.white,
                      textColor: context.appColors.white,
                    ),
                    FilledButton.tonal(
                      onPressed: () => context.pop(),
                      style: FilledButton.styleFrom(
                        backgroundColor: Colors.transparent,
                      ),
                      child: Text(
                        'Back',
                        style: context.typography.mediumSemi.copyWith(
                          color: context.appColors.white,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
