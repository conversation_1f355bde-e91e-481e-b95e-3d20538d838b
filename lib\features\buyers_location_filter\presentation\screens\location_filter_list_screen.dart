import 'dart:developer';

import 'package:buyer_board/common/widgets/app_search_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/buyers_location_filter/domain/entities/location_entity.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/buyer_location_filter_cubit.dart';
import 'package:buyer_board/features/buyers_location_filter/presentation/cubit/device_current_location_cubit.dart';
import 'package:buyer_board/features/home/<USER>/cubit/home_tab_cubit.dart';
import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:buyer_board/features/settings/presentation/cubit/share_location_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class LocationFilterListScreen extends StatefulWidget {
  const LocationFilterListScreen({
    super.key,
    required this.onLocationSelected,
    required this.selectedLocation,
  });

  final void Function(LocationEntity) onLocationSelected;
  final LocationEntity? selectedLocation;

  @override
  State<LocationFilterListScreen> createState() =>
      _LocationFilterListScreenState();
}

class _LocationFilterListScreenState extends State<LocationFilterListScreen> {
  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    final xColors = context.theme.appColors;
    final typography = context.typography;
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Column(
        children: [
          spacerH12,
          Container(
            height: 5,
            width: 64,
            decoration: BoxDecoration(
              color: colors.onSurface,
              borderRadius: BorderRadius.circular(100),
            ),
          ),
          spacerH12,
          Text(
            'Location Selection',
            style: typography.large2xBlack.copyWith(
              color: colors.onSurface,
            ),
          ),
          spacerH8,
          Divider(
            color: xColors.greyXLightPXDark,
            thickness: 1,
            height: 0,
          ),
          _LocationsData(
            selectedLocation: widget.selectedLocation,
            onLocationSelected: (location) {
              widget.onLocationSelected(location);
            },
          ),
        ],
      ),
    );
  }
}

class _LocationsData extends StatefulWidget {
  const _LocationsData({
    required this.selectedLocation,
    required this.onLocationSelected,
  });

  final LocationEntity? selectedLocation;
  final void Function(LocationEntity) onLocationSelected;

  @override
  State<_LocationsData> createState() => _LocationsDataState();
}

class _LocationsDataState extends State<_LocationsData> {
  late final ScrollController _scrollController;
  @override
  void initState() {
    super.initState();
    context.read<BuyerFilterLocationsCubit>().getLocations();
    _scrollController = ScrollController()
      ..addListener(() {
        if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent * 0.9) {
          context
              .read<BuyerFilterLocationsCubit>()
              .getLocations(loadNextPage: true);
        }
      });
  }

  // @override
  // void dispose() {
  //   _scrollController.dispose();
  //   context.read<BuyerFilterLocationsCubit>().close();
  //   super.dispose();
  // }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BuyerFilterLocationsCubit, BuyerLocationFilterState>(
      builder: (context, state) {
        return switch (state) {
          BuyerLocationFilterError(message: String message) => Center(
              child: Text(
                message,
                style: AppStyles.medium.copyWith(
                  color: context.colorScheme.error,
                ),
              ),
            ),
          BuyerLocationFilterLoaded(
            locations: List<LocationEntity> locations
          ) =>
            _List(
              locations: locations,
              selectedLocation: widget.selectedLocation,
              scrollController: _scrollController,
              onLocationSelected: widget.onLocationSelected,
            ),
          _ => SizedBox(
              height: MediaQuery.sizeOf(context).height * 0.5,
              child: const Center(child: CupertinoActivityIndicator()),
            ),
        };
      },
    );
  }
}

class _List extends StatefulWidget {
  const _List({
    required this.locations,
    required this.selectedLocation,
    required this.onLocationSelected,
    required this.scrollController,
  });
  final List<LocationEntity> locations;
  final LocationEntity? selectedLocation;
  final void Function(LocationEntity) onLocationSelected;
  final ScrollController scrollController;

  @override
  State<_List> createState() => _ListState();
}

class _ListState extends State<_List> {
  late List<LocationEntity> locations;
  String? searchItem;

  @override
  void initState() {
    super.initState();
    locations = widget.locations;
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    final typography = context.typography;
    log(locations.toString());
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 4,
            ),
            child: AppSearchField(
              onChanged: (value) {
                if (value.length > 2) {
                  context
                      .read<BuyerFilterLocationsCubit>()
                      .searchLocations(value);
                } else if (value.isEmpty) {
                  context.read<BuyerFilterLocationsCubit>().getLocations();
                }
              },
            ),
          ),
          locations.isEmpty
              ? Column(
                  children: [
                    spacerH32,
                    Text(
                      'No Buyers available...',
                      style: AppStyles.medium.copyWith(
                        color: colors.onSurface,
                      ),
                    ),
                    spacerH16,
                    TextButton(
                      onPressed: () {
                        context.pop();
                        context.read<HomeBottomNarBarTabCubit>().changeTab(2);
                      },
                      child: const Text('Add a buyer'),
                    ),
                  ],
                )
              : BlocBuilder<ShareLocationCubit, bool>(
                  builder: (context, locationSharing) {
                  return BlocBuilder<DeviceCurrentLocationCubit,
                      DeviceCurrentLocationState>(builder: (context, state) {
                    LocationEntity? userLocation;
                    if (state is DeviceCurrentLocationLoaded &&
                        locationSharing) {
                      userLocation = state.location;
                    }
                    final user = context.read<ProfileCubit>().getUserProfile();

                    if (!locationSharing &&
                        user?.profile?.brokerageZipCode != null) {
                      userLocation = LocationEntity(
                        zipCode: user?.profile?.brokerageZipCode ?? '',
                        cityName: user?.profile?.brokerageCity ?? '',
                        stateName: user?.profile?.brokerageState ?? '',
                        latitude: -1,
                        longitude: -1,
                        stateId: '',
                      );
                    }
                    final updatedList = (userLocation != null
                        ? [userLocation, ...locations]
                        : locations);
                    return Expanded(
                      child: Material(
                        type: MaterialType.transparency,
                        child: ListView.builder(
                          itemCount: updatedList.length,
                          controller: widget.scrollController,
                          itemBuilder: (context, index) {
                            final currentLocation = updatedList[index];
                            final isSelected =
                                widget.selectedLocation != null &&
                                    widget.selectedLocation?.zipCode ==
                                        currentLocation.zipCode;
                            final backgroundColor = isSelected
                                ? colors.primary
                                : Colors.transparent;
                            final textColor =
                                isSelected ? colors.onPrimary : colors.primary;
                            return ListTile(
                              dense: true,
                              visualDensity: VisualDensity.compact,
                              tileColor: index == 0
                                  ? Colors.transparent
                                  : backgroundColor,
                              title: Center(
                                child: RichText(
                                  text: TextSpan(
                                    children: [
                                      if (userLocation != null && index == 0)
                                        TextSpan(
                                          text: 'Current: ',
                                          style: typography.largeReg
                                              .copyWith(color: index==0?colors.primary:textColor),
                                        ),
                                      TextSpan(
                                        text: '${currentLocation.zipCode} ',
                                        style: typography.largeSemi
                                            .copyWith(color: index==0?colors.primary:textColor),
                                      ),
                                      TextSpan(
                                        text: currentLocation.cityName,
                                        style: typography.largeReg
                                            .copyWith(color: index==0?colors.primary:textColor),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              onTap: () {
                                widget.onLocationSelected(currentLocation);
                              },
                            );
                          },
                        ),
                      ),
                    );
                  });
                }),
        ],
      ),
    );
  }
}
