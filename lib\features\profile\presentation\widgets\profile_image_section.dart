import 'dart:io';

import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/resources/resources.dart';

class ProfileImageSection extends StatefulWidget {
  const ProfileImageSection({super.key, this.profileImage});
  final String? profileImage;
  @override
  State<ProfileImageSection> createState() => _ProfileImageSectionState();
}

class _ProfileImageSectionState extends State<ProfileImageSection> {
  String? galleryImage;
  Future<void> selectProfileImage() async {
    final result = await context.read<ProfileCubit>().selectProfileImage();
    if (result != null) {
      setState(() {
        galleryImage = result;
      });
      if (mounted) {
        await context.read<ProfileCubit>().updateUserProfile();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorScheme;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: Dimensions.materialPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: colorScheme.surfaceContainer,
      ),
      height: 232,
      width: double.maxFinite,
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(100),
          child: InkWell(
            onTap: () async {
              selectProfileImage();
            },
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    borderRadius: BorderRadius.circular(100),
                  ),
                  child: galleryImage != null
                      ? Image.file(File(galleryImage!), fit: BoxFit.cover)
                      : widget.profileImage != null
                          ? Image.network(widget.profileImage!,
                              fit: BoxFit.cover)
                          : Image.asset(
                              Drawables.person,
                              color: colorScheme.surface,
                            ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  height: 40,
                  width: 200,
                  decoration: BoxDecoration(
                    color: AppColors.black.withOpacity(.5),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    galleryImage != null
                        ? Strings.edit
                        : widget.profileImage != null
                            ? Strings.edit
                            : 'Add',
                    textAlign: TextAlign.center,
                    style: AppStyles.large.copyWith(
                      color: AppColors.white,
                      height: 0.8,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
