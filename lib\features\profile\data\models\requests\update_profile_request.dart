import 'package:json_annotation/json_annotation.dart';
part 'update_profile_request.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
  includeIfNull: false,
)
class UpdateProfileRequest {
  UpdateProfileRequest({
    this.email,
    this.avatar,
    this.firstName,
    this.lastName,
    this.primaryPhoneNumber,
    this.brokerageName,
    this.brokerageLisenceNo,
    this.brokerageStreetAddress,
    this.brokerageCity,
    this.brokerageZipCode,
    this.brokerageState,
    this.agentLicenseIdNo,
    this.imageUrl,
  });
  @<PERSON><PERSON><PERSON><PERSON>(name: "agent_email")
  final String? email;
  final String? avatar;
  final String? firstName;
  final String? lastName;
  @<PERSON><PERSON><PERSON><PERSON>(name: "phone_number_primary")
  final String? primaryPhoneNumber;
  final String? brokerageName;
  final String? brokerageLisenceNo;
  @Json<PERSON>ey(name: "broker_street_address")
  final String? brokerageStreetAddress;
  @<PERSON><PERSON><PERSON><PERSON>(name: "broker_city")
  final String? brokerageCity;
  final String? brokerageZipCode;
  final String? brokerageState;
  @Json<PERSON>ey(name: 'lisence_id_no')
  final String? agentLicenseIdNo;
  final String? imageUrl;

  factory UpdateProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateProfileRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateProfileRequestToJson(this);
}
