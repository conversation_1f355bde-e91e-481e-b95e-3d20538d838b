import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/resources/dimens.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/onboarding/data/onboarding_model.dart';
import 'package:buyer_board/features/onboarding/presentation/cubit/onboarding_cubit.dart';
import 'package:buyer_board/features/onboarding/presentation/widgets/onboarding_bullets.dart';
import 'package:buyer_board/features/onboarding/presentation/widgets/onboarding_page_indicators.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/resources/resources.dart';
import '../states/onboarding_state.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: BlocBuilder<OnBoardingCubit, OnBoardingState>(
          builder: (context, state) => state.maybeWhen(
                success: (onboardings) => OnBoardingBuilder(
                  onboardings: onboardings,
                ),
                orElse: () => const SizedBox.shrink(),
              )),
    );
  }
}

class OnBoardingBuilder extends StatelessWidget {
  const OnBoardingBuilder(
      {super.key,
      required this.onboardings,
      });
  final List<OnboardingModel> onboardings;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
        valueListenable: onBoardingScreenIndexNotifier,
        builder: (context, index, _) {
          return Column(
            children: [
              Image.asset(onboardings[index].image),
              const SizedBox(height: 30),
              OnBoardingPageIndicators(selectedIndex: index),
              const SizedBox(height: 16),
              if (onboardings[index].leading != null)
                Text(
                  onboardings[index].leading!,
                  style:
                      AppStyles.mediumSemiBold.copyWith(color: AppColors.white),
                ),
              Text(
                onboardings[index].heading!,
                style: AppStyles.largeBold2x.copyWith(color: AppColors.white),
              ),
              const SizedBox(height: 32),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.padding_24),
                child: Text(
                  onboardings[index].description!,
                  style: AppStyles.large
                      .copyWith(color: AppColors.white, fontSize: 20),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 32),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.padding_24),
                child: OnBoardingBullets(
                  bullets: onboardings[index].bullets ?? [],
                ),
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: Dimensions.padding_24),
                child: CommonButton.basic(
                  label: index < 3 ? "Skip" : "Done",
                  action: () {
                    index < 3
                        ? onBoardingScreenIndexNotifier.value = index + 1
                        :  {
                                Injector.resolve<AppPreferences>()
                                    .setupUserOnboardedStatus(),
                                context.go(PagePath.mainScreen),
                              };
                  },
                  textColor: AppColors.primary,
                ),
              ),
              const SizedBox(height: 64)
            ],
          );
        });
  }
}
