import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';

class AppTermsAndPolicyWidget extends StatelessWidget {
  const AppTermsAndPolicyWidget({super.key});

  // /Users/<USER>/Development/projects_flutter/buyerboard_mobile/android/app/keystore.jks

  final termsOfServiceLink = 'https://buyerboard.com/terms';
  final privacyPolicyLink = 'https://buyerboard.com/privacy';

  // This function will open the Terms of Service link
  Future<void> _goToTermsOfService(BuildContext context) async {
    final uri = Uri.parse(termsOfServiceLink);
    CoreUtils.launchUri(context: context, uri: uri);
    // context.push(PagePath.termsOfService);
  }

  // This function will open the Privacy Policy link
  Future<void> _goToPrivacyPolicy(BuildContext context) async {
    final uri = Uri.parse(privacyPolicyLink);
    CoreUtils.launchUri(context: context, uri: uri);
    // context.push(PagePath.privacyPolicy);
  }

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: context.typography.smallReg.copyWith(
          color: context.appColors.white,
        ),
        children: [
          const TextSpan(
            text: 'By proceeding, I am agreeing to BuyerBoard’s ',
          ),
          TextSpan(
            recognizer: TapGestureRecognizer()
              ..onTap = () => _goToTermsOfService(context),
            text: 'Terms of Service ',
            style: context.typography.smallBlack.copyWith(
              color: context.appColors.white,
            ),
          ),
          TextSpan(
            text: 'and ',
            style: context.typography.smallReg.copyWith(
              color: context.appColors.white,
            ),
          ),
          TextSpan(
            recognizer: TapGestureRecognizer()
              ..onTap = () => _goToPrivacyPolicy(context),
            text: 'Privacy Policy.',
            style: context.typography.smallBlack.copyWith(
              color: context.appColors.white,
            ),
          ),
        ],
      ),
    );
  }
}
