import 'dart:developer';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/network/interceptors/error_handler_interceptor.dart';
import 'package:buyer_board/core/utils/local_auth_util.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/auth/data/models/requests/signup_auth_request.dart';
import 'package:buyer_board/features/auth/presentation/cubit/auth_navigation_cubit.dart';
import 'package:buyer_board/features/auth/presentation/state/auth_navigation_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/repository/auth_repository.dart';
import '../state/sign_up_with_email_state.dart';

class SignUpWithEmailCubit extends Cubit<SignUpWithEmailState> {
  SignUpWithEmailCubit({
    required this.authRepository,
    required this.appPreferences,
    required this.localAuth,
  }) : super(const SignUpWithEmailState.initial());
  final AuthRepository authRepository;
  final AppPreferences appPreferences;
  final LocalAuthUtil localAuth;

  Future<bool> canAuthenticate() async => await localAuth.canAuthenticate();

  void _saveUserCredentials(String email, String password) =>
      appPreferences.saveUserCredentials(email: email, password: password);

  Future<void> registerWithEmailAndPassword(
    BuildContext context, {
    required String email,
    required String firstName,
    required String lastName,
    required String password,
    bool rememberMe = false,
    bool enabledBiometric = false,
  }) async {
    emit(const SignUpWithEmailState.loading());
    try {
      final response = await authRepository.signUpWithEmailPassword(
        signUpWithEmailPasswordRequest: SignUpAuthRequest(
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
        ),
      );
      log("errorrrrrrr$response");
      if (response.data != null) {
        rememberMe
            ? {
                appPreferences.setUser(response.data!),
                emit(SignUpWithEmailState.rememberMe(
                    response.message.body ?? '')),
              }
            : enabledBiometric
                ? {
                    _saveUserCredentials(email, password),
                    emit(SignUpWithEmailState.rememberMe(
                        response.message.body ?? '')),
                  }
                : {
                    if (context.mounted)
                      {
                        context
                            .read<UserSessionCubit>()
                            .setUser(response.data!),
                        emit(SignUpWithEmailState.success(
                            response.data!, response.message.body ?? '')),
                      }
                  };
      }
      if (context.mounted) {
        context
            .read<UserAuthRouteCubit>()
            .changeNavigationRoute(UserAuthRouteStateSignUp());
      }
    } on ErrorObjectException catch (e) {
      String errorMessage;
      try {
        final errorData = e.errorObject;
        if (errorData != null && errorData.isNotEmpty) {
          final firstErrorKey = errorData.keys.first;
          final firstErrorMessage = errorData[firstErrorKey];
          if (firstErrorMessage is String) {
            errorMessage = firstErrorMessage;
          } else {
            errorMessage = "An unexpected error occurred.";
          }
        } else {
          errorMessage = "An unexpected error occurred.";
        }
      } catch (_) {
        errorMessage =
            "An error occurred. Please check your input and try again.";
      }
      emit(SignUpWithEmailState.signupError(errorMessage));
    } catch (e) {
      emit(SignUpWithEmailState.signupError(
          "$e"));
    }
  }
}
