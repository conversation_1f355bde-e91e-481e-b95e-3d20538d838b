import 'package:buyer_board/features/chat/data/models/chat_message.dart';

sealed class ChatState {}

final class ChatInitialState extends ChatState {}

final class ChatLoadingState extends ChatState {}

final class ChatDataState extends ChatState {
  final List<ChatMessage> messages;
  ChatDataState(
    this.messages,
  );
}

final class ChatErrorState extends ChatState {
  final String message;
  ChatErrorState(
    this.message,
  );
}
