import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/network/rest_api_client.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/chat/data/datasources/chat_websocket_service.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/data/models/upload_attachments_model.dart';
import 'package:buyer_board/features/chat/domain/entities/chat_event_enum.dart';
import 'package:buyer_board/features/chat/domain/repositories/chat_repository.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_socket_connection_cubit.dart';
import 'package:logger/logger.dart';

final class ChatRepositoryImpl implements ChatRepository {
  final WebSocketService _webSocketService;
  final AppPreferences _appPreferences;
  final UserSessionCubit _userSessionCubit;
  final RestAPIClient restAPIClient;
  ChatRepositoryImpl(this._webSocketService, this.restAPIClient)
      : _appPreferences = Injector.resolve<AppPreferences>(),
        _userSessionCubit = Injector.resolve<UserSessionCubit>();

  Future<void> _sendPayload(ChatPayload payload) async {
    try {
      print("""
    Payload: ${payload.toJson()}
""");
      await _webSocketService.sendMessage(jsonEncode(payload.toJson()));
    } catch (e) {
      Logger().e(e);
      rethrow;
    }
  }

  ChatEventType _mapEvent(String event) {
    return switch (event) {
      'phx_reply' => ChatEventType.phxReply,
      'new_message' => ChatEventType.newMessage,
      'edit_message' => ChatEventType.editMessage,
      'delete_message' => ChatEventType.deleteMessage,
      'archive_chat' => ChatEventType.archiveChat,
      'unarchive_chat' => ChatEventType.unarchiveChat,
      _ => ChatEventType.other,
    };
  }

  @override
  Future<void> deleteMessages(ChatPayload payload) async {
    _sendPayload(payload);
    await for (final message in _webSocketService.stream) {
      final decodedResponse = jsonDecode(message);
      _mapEvent(decodedResponse['event']);
    }
  }

  final Completer<void> _connectionCompleter = Completer<void>();
  @override
  void initializeConnection() {
    final token =
        _appPreferences.getUser()?.token ?? _userSessionCubit.state?.token;

    if (token == null) {
      throw ChatSocketConnectionError('Token not found');
    }

    _webSocketService.connect(token).then((_) {
      if (!_connectionCompleter.isCompleted) {
        _connectionCompleter.complete();
      }
    }).catchError((error) {
      if (!_connectionCompleter.isCompleted) {
        _connectionCompleter.completeError(error);
      }
      throw ChatSocketConnectionError('Failed to connect to the WebSocket');
    });
  }

  @override
  Future<void> get connectionReady => _connectionCompleter.future;
  @override
  void closeConnection() => _webSocketService.disconnect();
  @override
  Stream<({ChatEventType event, List<ChatMessage> data})> getChat(
      ChatPayload payload) async* {
    _sendPayload(payload);
    try {
      await _sendPayload(payload);
    } catch (e) {
      yield (event: ChatEventType.error, data: []);
      return;
    }
    await for (final message in _webSocketService.stream) {
      final decodedResponse = jsonDecode(message);
      final event = _mapEvent(decodedResponse['event']);
      List response = [];
      switch (event) {
        case ChatEventType.deleteMessage:
          response = decodedResponse['payload']['message'] as List<dynamic>;
          break;
        case ChatEventType.editMessage:
        case ChatEventType.newMessage:
          if (decodedResponse['payload']['message'] != null &&
              decodedResponse['payload']['message'] is Map<String, dynamic>) {
            response = [
              decodedResponse['payload']['message'] as Map<String, dynamic>
            ];
          }
          break;
        case ChatEventType.phxReply:
          if (decodedResponse['payload']['response']['messages']
              is Map<String, dynamic>) {
            continue;
          }
          if (decodedResponse['payload']['response']['message']
              is Map<String, dynamic>) {
            continue;
          }
          if ((decodedResponse['payload']['response']
                  is Map<String, dynamic>) &&
              (decodedResponse['payload']['response'] as Map<String, dynamic>)
                  .containsKey('messages')) {
            if (decodedResponse['payload']['response']['messages']
                is List<dynamic>) {
              response = decodedResponse['payload']['response']['messages']
                  as List<dynamic>;
            }
          }
          break;
        default:
          response = [];
          break;
      }

      final messages = response.map((e) => ChatMessage.fromJson(e)).toList();
      yield (event: event, data: messages);
    }
  }

  @override
  Future<void> sendMessage(ChatPayload payload) async {
    try {
      await _sendPayload(payload);
    } catch (e) {
      Logger().e(e);
      rethrow;
    }
  }

  @override
  Future<UploadAttachment?> uploadAttachments({required File images}) async {
    try {
      final response = await restAPIClient.uploadAttachment(files: images);
      final List<UploadAttachment?> attachments = response.data ?? [];
      return attachments.isNotEmpty ? attachments.first : null;
    } catch (e) {
      print('Error uploading file: $e');
    }
    return null;
  }

  @override
  Future<String> deleteAttachments(
      {required List<Map<String, dynamic>> images}) async {
    try {
      final response = await restAPIClient.deleteAttachments(
        attachment: images,
      );
      final String message = response;
      return message;
    } catch (e) {
      print('Error delete files: $e');
    }
    return "";
  }
}
