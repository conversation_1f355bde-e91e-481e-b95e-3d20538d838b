import 'package:buyer_board/core/resources/page_path.dart';
import 'package:buyer_board/features/auth/presentation/cubit/logout_cubit.dart';
import 'package:buyer_board/features/settings/domain/repositories/settings_repository.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

sealed class DeleteAccountState {}

class DeleteAccountNone extends DeleteAccountState {}

class DeleteAccountDeleting extends DeleteAccountState {}

class DeleteAccountSuccess extends DeleteAccountState {}

class DeleteAccountError extends DeleteAccountState {}

class DeleteAccountCubit extends Cubit<DeleteAccountState> {
  final SettingsRepository repository;
  DeleteAccountCubit(this.repository) : super(DeleteAccountNone());
  Future<void> delete(BuildContext context) async {
    try {
      emit(DeleteAccountDeleting());
      await repository.deleteAccount();
      emit(DeleteAccountSuccess());
      if (context.mounted) {
        context.go(PagePath.authScreen);
      }
    } catch (e) {
      emit(DeleteAccountError());
    }
  }
}
