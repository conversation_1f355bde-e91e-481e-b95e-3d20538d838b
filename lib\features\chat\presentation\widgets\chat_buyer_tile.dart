import 'dart:ui';

import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/presentation/screens/chat_buyer_expanded_card.dart';
import 'package:flutter/material.dart';

class BuyerTileWidget extends StatelessWidget {
  const BuyerTileWidget({
    super.key,
    required this.buyerModel,
  });
  final ChatGroupModel buyerModel;

  Gradient _getImageOverlay(BuildContext context, ChatGroupModel buyer) {
    final colors = context.colorScheme;
    final isMyBuyer = buyer.myBuyer;
    final color = isMyBuyer ? colors.primary : colors.inverseSurface;
    return LinearGradient(
      colors: [
        color,
        color.withOpacity(0.3),
      ],
      stops: const [0.3, 0.7],
      begin: AlignmentDirectional.centerStart,
      end: AlignmentDirectional.centerEnd,
    );
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    final isMyBuyer = buyerModel.myBuyer;
    final fullName = '${buyerModel.firstName} ${buyerModel.lastName}';
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ChatBuyerExpandedCard(
              id: buyerModel.buyerId,
            ),
          ),
        );
      },
      child: Container(
        height: 60,
        decoration: BoxDecoration(
          color: colors.surfaceContainerLow,
          boxShadow: [
            BoxShadow(
              color: colors.shadow.withOpacity(0.25),
              offset: const Offset(0, 4),
              blurRadius: 4,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: Stack(
            alignment: AlignmentDirectional.center,
            children: [
              _ImageWithBlurEffect(colors: colors, buyerModel: buyerModel),
              Positioned(
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    gradient: _getImageOverlay(context, buyerModel),
                    // color: Colors.red,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        isMyBuyer ? fullName : buyerModel.sku.toString(),
                        overflow: TextOverflow.ellipsis,
                        style: AppStyles.mediumBold.copyWith(
                          color: colors.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Icon(
                      Icons.arrow_forward,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ImageWithBlurEffect extends StatelessWidget {
  const _ImageWithBlurEffect({
    super.key,
    required this.colors,
    required this.buyerModel,
  });

  final ColorScheme colors;
  final ChatGroupModel buyerModel;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      child: Container(
        height: 44,
        decoration: BoxDecoration(
          color: colors.primary,
          borderRadius: BorderRadius.circular(4),
          image: const DecorationImage(
            // image: buyerModel.imageUrl != null
            //     ? NetworkImage(buyerModel.imageUrl!) as ImageProvider
            //     : const AssetImage(Drawables.townhouseImage),
            image: AssetImage(Drawables.townhouseImage),
            fit: BoxFit.cover,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 2.5, sigmaY: 2.5),
            child: Container(),
          ),
        ),
      ),
    );
  }
}
