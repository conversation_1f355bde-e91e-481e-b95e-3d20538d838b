// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'intro_to_buyer_board_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IntroToBuyerBoardState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<OnboardingModel> onBoardings) success,
    required TResult Function(String? error) onBoardingError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<OnboardingModel> onBoardings)? success,
    TResult? Function(String? error)? onBoardingError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<OnboardingModel> onBoardings)? success,
    TResult Function(String? error)? onBoardingError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(onBoardingError value) onBoardingError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(onBoardingError value)? onBoardingError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(onBoardingError value)? onBoardingError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IntroToBuyerBoardStateCopyWith<$Res> {
  factory $IntroToBuyerBoardStateCopyWith(IntroToBuyerBoardState value,
          $Res Function(IntroToBuyerBoardState) then) =
      _$IntroToBuyerBoardStateCopyWithImpl<$Res, IntroToBuyerBoardState>;
}

/// @nodoc
class _$IntroToBuyerBoardStateCopyWithImpl<$Res,
        $Val extends IntroToBuyerBoardState>
    implements $IntroToBuyerBoardStateCopyWith<$Res> {
  _$IntroToBuyerBoardStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$IntroToBuyerBoardStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'IntroToBuyerBoardState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<OnboardingModel> onBoardings) success,
    required TResult Function(String? error) onBoardingError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<OnboardingModel> onBoardings)? success,
    TResult? Function(String? error)? onBoardingError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<OnboardingModel> onBoardings)? success,
    TResult Function(String? error)? onBoardingError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(onBoardingError value) onBoardingError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(onBoardingError value)? onBoardingError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(onBoardingError value)? onBoardingError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements IntroToBuyerBoardState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$loadingImplCopyWith<$Res> {
  factory _$$loadingImplCopyWith(
          _$loadingImpl value, $Res Function(_$loadingImpl) then) =
      __$$loadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$loadingImplCopyWithImpl<$Res>
    extends _$IntroToBuyerBoardStateCopyWithImpl<$Res, _$loadingImpl>
    implements _$$loadingImplCopyWith<$Res> {
  __$$loadingImplCopyWithImpl(
      _$loadingImpl _value, $Res Function(_$loadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$loadingImpl implements loading {
  const _$loadingImpl();

  @override
  String toString() {
    return 'IntroToBuyerBoardState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$loadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<OnboardingModel> onBoardings) success,
    required TResult Function(String? error) onBoardingError,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<OnboardingModel> onBoardings)? success,
    TResult? Function(String? error)? onBoardingError,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<OnboardingModel> onBoardings)? success,
    TResult Function(String? error)? onBoardingError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(onBoardingError value) onBoardingError,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(onBoardingError value)? onBoardingError,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(onBoardingError value)? onBoardingError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class loading implements IntroToBuyerBoardState {
  const factory loading() = _$loadingImpl;
}

/// @nodoc
abstract class _$$successImplCopyWith<$Res> {
  factory _$$successImplCopyWith(
          _$successImpl value, $Res Function(_$successImpl) then) =
      __$$successImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<OnboardingModel> onBoardings});
}

/// @nodoc
class __$$successImplCopyWithImpl<$Res>
    extends _$IntroToBuyerBoardStateCopyWithImpl<$Res, _$successImpl>
    implements _$$successImplCopyWith<$Res> {
  __$$successImplCopyWithImpl(
      _$successImpl _value, $Res Function(_$successImpl) _then)
      : super(_value, _then);

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? onBoardings = null,
  }) {
    return _then(_$successImpl(
      null == onBoardings
          ? _value._onBoardings
          : onBoardings // ignore: cast_nullable_to_non_nullable
              as List<OnboardingModel>,
    ));
  }
}

/// @nodoc

class _$successImpl implements success {
  const _$successImpl(final List<OnboardingModel> onBoardings)
      : _onBoardings = onBoardings;

  final List<OnboardingModel> _onBoardings;
  @override
  List<OnboardingModel> get onBoardings {
    if (_onBoardings is EqualUnmodifiableListView) return _onBoardings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_onBoardings);
  }

  @override
  String toString() {
    return 'IntroToBuyerBoardState.success(onBoardings: $onBoardings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$successImpl &&
            const DeepCollectionEquality()
                .equals(other._onBoardings, _onBoardings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_onBoardings));

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      __$$successImplCopyWithImpl<_$successImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<OnboardingModel> onBoardings) success,
    required TResult Function(String? error) onBoardingError,
  }) {
    return success(onBoardings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<OnboardingModel> onBoardings)? success,
    TResult? Function(String? error)? onBoardingError,
  }) {
    return success?.call(onBoardings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<OnboardingModel> onBoardings)? success,
    TResult Function(String? error)? onBoardingError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(onBoardings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(onBoardingError value) onBoardingError,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(onBoardingError value)? onBoardingError,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(onBoardingError value)? onBoardingError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class success implements IntroToBuyerBoardState {
  const factory success(final List<OnboardingModel> onBoardings) =
      _$successImpl;

  List<OnboardingModel> get onBoardings;

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$onBoardingErrorImplCopyWith<$Res> {
  factory _$$onBoardingErrorImplCopyWith(_$onBoardingErrorImpl value,
          $Res Function(_$onBoardingErrorImpl) then) =
      __$$onBoardingErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$onBoardingErrorImplCopyWithImpl<$Res>
    extends _$IntroToBuyerBoardStateCopyWithImpl<$Res, _$onBoardingErrorImpl>
    implements _$$onBoardingErrorImplCopyWith<$Res> {
  __$$onBoardingErrorImplCopyWithImpl(
      _$onBoardingErrorImpl _value, $Res Function(_$onBoardingErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$onBoardingErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$onBoardingErrorImpl implements onBoardingError {
  const _$onBoardingErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'IntroToBuyerBoardState.onBoardingError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$onBoardingErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$onBoardingErrorImplCopyWith<_$onBoardingErrorImpl> get copyWith =>
      __$$onBoardingErrorImplCopyWithImpl<_$onBoardingErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<OnboardingModel> onBoardings) success,
    required TResult Function(String? error) onBoardingError,
  }) {
    return onBoardingError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<OnboardingModel> onBoardings)? success,
    TResult? Function(String? error)? onBoardingError,
  }) {
    return onBoardingError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<OnboardingModel> onBoardings)? success,
    TResult Function(String? error)? onBoardingError,
    required TResult orElse(),
  }) {
    if (onBoardingError != null) {
      return onBoardingError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(onBoardingError value) onBoardingError,
  }) {
    return onBoardingError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(onBoardingError value)? onBoardingError,
  }) {
    return onBoardingError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(onBoardingError value)? onBoardingError,
    required TResult orElse(),
  }) {
    if (onBoardingError != null) {
      return onBoardingError(this);
    }
    return orElse();
  }
}

abstract class onBoardingError implements IntroToBuyerBoardState {
  const factory onBoardingError(final String? error) = _$onBoardingErrorImpl;

  String? get error;

  /// Create a copy of IntroToBuyerBoardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$onBoardingErrorImplCopyWith<_$onBoardingErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
