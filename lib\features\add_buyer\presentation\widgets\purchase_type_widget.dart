import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:flutter/material.dart';

import '../../../../common/widgets/common_radio_tile.dart';
import '../../../../core/resources/resources.dart';
import '../cubit/buyer_info_cubit.dart';

class PurchaseTypeWidget extends StatefulWidget {
  const PurchaseTypeWidget({super.key});

  @override
  State<PurchaseTypeWidget> createState() => _PurchaseTypeWidgetState();
}

class _PurchaseTypeWidgetState extends State<PurchaseTypeWidget> {
  void toglePurchaseType(PurchaseType? val) {
    final type = purchaseTypeNotifier.value;
    type != val
        ? purchaseTypeNotifier.value = val!
        : purchaseTypeNotifier.value = null;
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: purchaseTypeNotifier,
      builder: (context, purchaseType, _) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            Strings.purchaseType,
            style: context.typography.smallReg.copyWith(
              color: context.appColors.greyM,
            ),
          ),
          const SizedBox(height: 16),
          CommonRadioTile<PurchaseType?>(
            label: PurchaseType.purchase.label,
            value: PurchaseType.purchase,
            onChange: toglePurchaseType,
            groupValue: purchaseType,
          ),
          const SizedBox(height: 2),
          CommonRadioTile<PurchaseType?>(
            label: PurchaseType.lease.label,
            value: PurchaseType.lease,
            onChange: toglePurchaseType,
            groupValue: purchaseType,
          ),
          ValueListenableBuilder(
              valueListenable: purchaseTypeError,
              builder: (context, financialStatus, _) {
                return purchaseTypeError.value!.isNotEmpty
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 2),
                          Container(
                            height: 1,
                            width: double.infinity,
                            color: AppColors.errorMedium,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              "Field Required",
                              style: AppStyles.small.copyWith(
                                  color: AppColors.errorMedium,
                                  fontSize: 11,
                                  fontWeight: FontWeight.w700),
                            ),
                          ),
                        ],
                      )
                    : const SizedBox();
              })
        ],
      ),
    );
  }
}
