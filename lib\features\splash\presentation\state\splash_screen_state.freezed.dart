// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'splash_screen_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SplashScreenState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String route) success,
    required TResult Function() localAuth,
    required TResult Function(String? error) splashError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String route)? success,
    TResult? Function()? localAuth,
    TResult? Function(String? error)? splashError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String route)? success,
    TResult Function()? localAuth,
    TResult Function(String? error)? splashError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(success value) success,
    required TResult Function(localAuth value) localAuth,
    required TResult Function(splashError value) splashError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(success value)? success,
    TResult? Function(localAuth value)? localAuth,
    TResult? Function(splashError value)? splashError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(success value)? success,
    TResult Function(localAuth value)? localAuth,
    TResult Function(splashError value)? splashError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SplashScreenStateCopyWith<$Res> {
  factory $SplashScreenStateCopyWith(
          SplashScreenState value, $Res Function(SplashScreenState) then) =
      _$SplashScreenStateCopyWithImpl<$Res, SplashScreenState>;
}

/// @nodoc
class _$SplashScreenStateCopyWithImpl<$Res, $Val extends SplashScreenState>
    implements $SplashScreenStateCopyWith<$Res> {
  _$SplashScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$SplashScreenStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'SplashScreenState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String route) success,
    required TResult Function() localAuth,
    required TResult Function(String? error) splashError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String route)? success,
    TResult? Function()? localAuth,
    TResult? Function(String? error)? splashError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String route)? success,
    TResult Function()? localAuth,
    TResult Function(String? error)? splashError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(success value) success,
    required TResult Function(localAuth value) localAuth,
    required TResult Function(splashError value) splashError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(success value)? success,
    TResult? Function(localAuth value)? localAuth,
    TResult? Function(splashError value)? splashError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(success value)? success,
    TResult Function(localAuth value)? localAuth,
    TResult Function(splashError value)? splashError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements SplashScreenState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$successImplCopyWith<$Res> {
  factory _$$successImplCopyWith(
          _$successImpl value, $Res Function(_$successImpl) then) =
      __$$successImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String route});
}

/// @nodoc
class __$$successImplCopyWithImpl<$Res>
    extends _$SplashScreenStateCopyWithImpl<$Res, _$successImpl>
    implements _$$successImplCopyWith<$Res> {
  __$$successImplCopyWithImpl(
      _$successImpl _value, $Res Function(_$successImpl) _then)
      : super(_value, _then);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? route = null,
  }) {
    return _then(_$successImpl(
      null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$successImpl implements success {
  const _$successImpl(this.route);

  @override
  final String route;

  @override
  String toString() {
    return 'SplashScreenState.success(route: $route)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$successImpl &&
            (identical(other.route, route) || other.route == route));
  }

  @override
  int get hashCode => Object.hash(runtimeType, route);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      __$$successImplCopyWithImpl<_$successImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String route) success,
    required TResult Function() localAuth,
    required TResult Function(String? error) splashError,
  }) {
    return success(route);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String route)? success,
    TResult? Function()? localAuth,
    TResult? Function(String? error)? splashError,
  }) {
    return success?.call(route);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String route)? success,
    TResult Function()? localAuth,
    TResult Function(String? error)? splashError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(route);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(success value) success,
    required TResult Function(localAuth value) localAuth,
    required TResult Function(splashError value) splashError,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(success value)? success,
    TResult? Function(localAuth value)? localAuth,
    TResult? Function(splashError value)? splashError,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(success value)? success,
    TResult Function(localAuth value)? localAuth,
    TResult Function(splashError value)? splashError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class success implements SplashScreenState {
  const factory success(final String route) = _$successImpl;

  String get route;

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$localAuthImplCopyWith<$Res> {
  factory _$$localAuthImplCopyWith(
          _$localAuthImpl value, $Res Function(_$localAuthImpl) then) =
      __$$localAuthImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$localAuthImplCopyWithImpl<$Res>
    extends _$SplashScreenStateCopyWithImpl<$Res, _$localAuthImpl>
    implements _$$localAuthImplCopyWith<$Res> {
  __$$localAuthImplCopyWithImpl(
      _$localAuthImpl _value, $Res Function(_$localAuthImpl) _then)
      : super(_value, _then);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$localAuthImpl implements localAuth {
  const _$localAuthImpl();

  @override
  String toString() {
    return 'SplashScreenState.localAuth()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$localAuthImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String route) success,
    required TResult Function() localAuth,
    required TResult Function(String? error) splashError,
  }) {
    return localAuth();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String route)? success,
    TResult? Function()? localAuth,
    TResult? Function(String? error)? splashError,
  }) {
    return localAuth?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String route)? success,
    TResult Function()? localAuth,
    TResult Function(String? error)? splashError,
    required TResult orElse(),
  }) {
    if (localAuth != null) {
      return localAuth();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(success value) success,
    required TResult Function(localAuth value) localAuth,
    required TResult Function(splashError value) splashError,
  }) {
    return localAuth(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(success value)? success,
    TResult? Function(localAuth value)? localAuth,
    TResult? Function(splashError value)? splashError,
  }) {
    return localAuth?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(success value)? success,
    TResult Function(localAuth value)? localAuth,
    TResult Function(splashError value)? splashError,
    required TResult orElse(),
  }) {
    if (localAuth != null) {
      return localAuth(this);
    }
    return orElse();
  }
}

abstract class localAuth implements SplashScreenState {
  const factory localAuth() = _$localAuthImpl;
}

/// @nodoc
abstract class _$$splashErrorImplCopyWith<$Res> {
  factory _$$splashErrorImplCopyWith(
          _$splashErrorImpl value, $Res Function(_$splashErrorImpl) then) =
      __$$splashErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$splashErrorImplCopyWithImpl<$Res>
    extends _$SplashScreenStateCopyWithImpl<$Res, _$splashErrorImpl>
    implements _$$splashErrorImplCopyWith<$Res> {
  __$$splashErrorImplCopyWithImpl(
      _$splashErrorImpl _value, $Res Function(_$splashErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$splashErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$splashErrorImpl implements splashError {
  const _$splashErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'SplashScreenState.splashError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$splashErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$splashErrorImplCopyWith<_$splashErrorImpl> get copyWith =>
      __$$splashErrorImplCopyWithImpl<_$splashErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String route) success,
    required TResult Function() localAuth,
    required TResult Function(String? error) splashError,
  }) {
    return splashError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String route)? success,
    TResult? Function()? localAuth,
    TResult? Function(String? error)? splashError,
  }) {
    return splashError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String route)? success,
    TResult Function()? localAuth,
    TResult Function(String? error)? splashError,
    required TResult orElse(),
  }) {
    if (splashError != null) {
      return splashError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(success value) success,
    required TResult Function(localAuth value) localAuth,
    required TResult Function(splashError value) splashError,
  }) {
    return splashError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(success value)? success,
    TResult? Function(localAuth value)? localAuth,
    TResult? Function(splashError value)? splashError,
  }) {
    return splashError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(success value)? success,
    TResult Function(localAuth value)? localAuth,
    TResult Function(splashError value)? splashError,
    required TResult orElse(),
  }) {
    if (splashError != null) {
      return splashError(this);
    }
    return orElse();
  }
}

abstract class splashError implements SplashScreenState {
  const factory splashError(final String? error) = _$splashErrorImpl;

  String? get error;

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$splashErrorImplCopyWith<_$splashErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
