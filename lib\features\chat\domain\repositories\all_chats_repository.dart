import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/domain/entities/chat_event_enum.dart';

abstract interface class AllChatsRepository {
  // Method to establish the WebSocket connection
  void initializeConnection();

  // Method to close the WebSocket connection
  void closeConnection();

  // Method to get all conversations
  Stream<({ChatEventType event, List<ChatGroupModel> data})> getAllChats();

  // Method to archive a chat between two people
  Future<void> archiveChat(ChatPayload payload);
}
