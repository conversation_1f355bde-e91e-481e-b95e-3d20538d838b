import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chat_states.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_events.dart';
import 'package:buyer_board/features/chat/presentation/widgets/chat_expandable_thread.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class ChatMainScreen extends StatefulWidget {
  const ChatMainScreen({super.key});

  @override
  State<ChatMainScreen> createState() => _ChatMainScreenState();
}

class _ChatMainScreenState extends State<ChatMainScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Chats'),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.menu),
            color: AppColors.white,
            onPressed: () {
              context.push(PagePath.menu);
            },
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const _ChatMainListing(),
                    Divider(
                      color: context.appColors.greyLightGreyDark,
                      height: 16,
                    ),
                    const _ArchivedChat(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ChatMainListing extends StatelessWidget {
  const _ChatMainListing();
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AllChatsBloc, AllChatsState>(builder: (context, state) {
      return switch (state) {
        AllChatsInitialState() || AllChatsLoadingState() => SizedBox(
            height: MediaQuery.sizeOf(context).height * .3,
            child: CupertinoActivityIndicator(
              color: context.appColors.pPXLight,
            ),
          ),
        AllChatsDataState(chats: List<ChatGroupModel> chats) => chats.isEmpty
            ? const SizedBox(
                height: 120,
                child: Center(
                  child: Text('No messages yet'),
                ),
              )
            : ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.all(8),
                itemBuilder: (_, index) => ChatExpandableThread(
                  chatBuyerModel: chats[index],
                  key: ValueKey(chats[index].sku),
                ),
                itemCount: chats.length,
              ),
        _ => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: MediaQuery.sizeOf(context).height * .15,
                ),
                const Icon(
                  Icons.wifi_off,
                  color: AppColors.primary,
                  size: 48,
                ),
                const SizedBox(height: 10),
                SizedBox(
                  width: 150,
                  child: CommonButton.basic(
                    label: Strings.reconnect,
                    action: () {
                      context
                          .read<AllChatsBloc>()
                          .add(const CloseAllChatConnections());
                      context.read<AllChatsBloc>().add(GetAllChats());
                    },
                    backgroundColor: context.theme.appColors.pPXLight,
                    textColor: context.theme.appColors.whitePXDark,
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  "Socket disconnected due to unstable Internet.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.grey,
                    fontSize: 15,
                  ),
                ),
                SizedBox(
                  height: MediaQuery.sizeOf(context).height * .15,
                ),
              ],
            ),
          ),
      };
    });
  }
}

class _ArchivedChat extends StatelessWidget {
  const _ArchivedChat({super.key});

  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final xColors = context.appColors;
    return InkWell(
      onTap: () {
        context.push(PagePath.chatArchiveScreen);
      },
      child: Padding(
        padding: const EdgeInsets.only(
          left: 16,
          right: 16,
          top: 16,
          bottom: 48,
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              Drawables.archived,
              colorFilter: ColorFilter.mode(
                xColors.pPXLight,
                BlendMode.srcIn,
              ),
            ),
            spacerW16,
            Text(
              'Archived',
              style: typography.largeSemi,
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 25,
              color: xColors.greyM,
            )
          ],
        ),
      ),
    );
  }
}
