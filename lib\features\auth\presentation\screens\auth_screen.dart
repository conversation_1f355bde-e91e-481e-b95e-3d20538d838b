import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/features/auth/presentation/widgets/call_to_action_section.dart';
import 'package:buyer_board/features/auth/presentation/widgets/login_form.dart';
import 'package:buyer_board/features/auth/presentation/widgets/sign_up_form.dart';
import 'package:buyer_board/features/splash/presentation/widgets/logo_and_headline_section.dart';
import 'package:flutter/material.dart';

enum AuthAction { none, login, signup }

ValueNotifier authActionNotifier = ValueNotifier<AuthAction>(AuthAction.none);

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key, this.localAuthEnabled = false});
  final bool localAuthEnabled;

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

Widget getAuthActionSection(AuthAction action,
    {bool localAuthEnabled = false}) {
  switch (action) {
    case AuthAction.login:
      return Expanded(
        child: Column(
          children: [
            const Spacer(),
            LoginForm(
              localAuthEnabled: localAuthEnabled,
            ),
          ],
        ),
      );
    case AuthAction.signup:
      return const Padding(
        padding: EdgeInsets.only(top: 24),
        child: SignUpForm(),
      );
    default:
      return const Expanded(
        child: Column(
          children: [
            Spacer(),
            CallToActionSction(),
          ],
        ),
      );
  }
}

class _AuthScreenState extends State<AuthScreen> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorScheme;
    final bottom = context.bottomInsets;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: colorScheme.primaryFixed,
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: SafeArea(
          child: SizedBox(
            child: LayoutBuilder(builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: IntrinsicHeight(
                    child: Column(
                      children: [
                        Visibility(
                          visible: bottom < 24,
                          child: const LogoAndHeadlineSection(),
                        ),
                        // const Spacer(),
                        ValueListenableBuilder(
                          valueListenable: authActionNotifier,
                          builder: (context, action, _) => getAuthActionSection(
                            action,
                            localAuthEnabled: widget.localAuthEnabled,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }
}
