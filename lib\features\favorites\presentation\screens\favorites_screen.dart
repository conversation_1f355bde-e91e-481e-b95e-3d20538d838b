import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/features/favorites/presentation/cubit/favourite_buyers_cubit.dart';
import 'package:buyer_board/features/favorites/presentation/state/favourite_buyer_state.dart';
import 'package:buyer_board/features/favorites/presentation/widgets/add_note_favorite_widget.dart';
import 'package:buyer_board/features/home/<USER>/widgets/buyer_card_item.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/resources/resources.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  @override
  void initState() {
    context.read<FavouriteBuyersCubit>().getFavouriteBuyers();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colors = context.colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: const Text(Strings.favorites),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.menu),
          color: AppColors.white,
          onPressed: () {
            context.push(PagePath.menu);
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Favorites List',
                  style: context.typography.large1xBlack.copyWith(
                    color: colors.onSurface,
                  )),
              Text('Find all of your favorited cards in the list below.',
                  style: context.typography.mediumReg.copyWith(
                    color: colors.onSurface,
                  )),
              spacerH32,
              BlocBuilder<FavouriteBuyersCubit, FavouriteBuyerState>(
                buildWhen: (_, state) => state.maybeWhen(
                  loading: () => true,
                  success: (buyer, message) => true,
                  orElse: () => false,
                ),
                builder: (context, state) {
                  return state.maybeWhen(
                    loading: () => Center(
                      child: CupertinoActivityIndicator(
                        radius: 16,
                        color: colors.primary,
                      ),
                    ),
                    success: (buyers, message) => buyers.isNotEmpty
                        ? ListView.separated(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemBuilder: (context, index) => Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // BuyerCardCollapsed(
                                //   buyer: buyers[index],
                                //   key: Key(buyers[index].id.toString()),
                                //   onFavouriteToggled: () {
                                //     if (mounted) {
                                //       context
                                //           .read<BuyersBloc>()
                                //           .add(ClearList());
                                //     }
                                //   },
                                // ),
                                BuyerCardItem(
                                  key: Key(buyers[index].id.toString()),
                                  buyer: buyers[index],
                                ),
                                AddNoteFavoriteWidget(buyer: buyers[index])
                              ],
                            ),
                            separatorBuilder: (ctx, index) => const SizedBox(height: 4),
                            itemCount: buyers.length,
                          )
                        : const NoFavouritesWidget(),
                    orElse: () => const SizedBox.shrink(),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NoFavouritesWidget extends StatelessWidget {
  const NoFavouritesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return DottedBorder(
      color: context.colorScheme.outline,
      radius: const Radius.circular(8),
      borderType: BorderType.RRect,
      dashPattern: const [4, 4],
      child: SizedBox(
        height: 166,
        width: double.maxFinite,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'No Favorites...',
              style: AppStyles.largeSemiBold.copyWith(
                color: context.colorScheme.onSurfaceVariant,
              ),
            ),
            Text(
              'Please add a buyer card to see it here.',
              style: AppStyles.medium.copyWith(
                color: context.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
