// import 'package:buyer_board/core/extensions/build_context.dart';
// import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
// import 'package:buyer_board/features/home/<USER>/widgets/bb_chip.dart';
// import 'package:flutter/material.dart';

// final Map<String, dynamic> _propertyType = {
//   "single_family_house": "Single-Family Home",
//   "townhouse": "Townhouse",
//   "condo": "Condo",
//   "apartment": "Apartment",
//   "multi_family_house": "Multi-Family Home",
//   "mobile": "Mobile",
//   "fixer": "Fixer"
// };
// final Map<String, dynamic> _timeLine = {
//   "asap": "ASAP",
//   "three_months": "3 MOs",
//   "six_months": "6 MOs",
//   "one_year_plus": "Open",
//   "open": "Open",
// };

// Widget timelineChip(String timeline) {
//   const padding = EdgeInsets.symmetric(horizontal: 08, vertical: 2);
//   return switch (timeline) {
//     'asap' => const BBChip.asap(padding: padding),
//     'three_months' => const BBChip.threeMonths(padding: padding),
//     'six_months' => const BBChip.sixMonths(padding: padding),
//     _ => const BBChip.open(padding: padding),
//   };
// }

// class PropertyTypeAndTimeLineWidget extends StatelessWidget {
//   const PropertyTypeAndTimeLineWidget({super.key, required this.buyer});
//   final BuyerModel buyer;

//   @override
//   Widget build(BuildContext context) {
//     final typography = context.typography;
//     return SingleChildScrollView(
//       scrollDirection: Axis.horizontal,
//       child: Row(
//         children: [
//           Text(
//             _propertyType[buyer.buyerNeeds?.propertyTypeStr] ?? "Property_Type",
//             style: typography.mediumSemi.copyWith(
//               color: context.appColors.whitePDark,
//             ),
//           ),
//           const SizedBox(width: 8),
//           // timelineChip(buyer.buyerNeeds?.timeline ?? "open"),
//           if (buyer.buyerNeeds?.purchaseTypeStr == "rent") ...[
//             const SizedBox(width: 8),
//             const BBChip.rental(
//               padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
//             )
//           ]
//         ],
//       ),
//     );
//   }
// }
