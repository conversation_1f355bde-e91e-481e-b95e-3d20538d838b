import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:flutter/material.dart';

class AppSwitch extends StatefulWidget {
  const AppSwitch({
    super.key,
    required this.title,
    required this.isActive,
    required this.onChanged,
  });

  final String? title;
  final bool isActive;
  final void Function(bool)? onChanged;

  @override
  State<AppSwitch> createState() => _AppSwitchState();
}

class _AppSwitchState extends State<AppSwitch> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = context.colorScheme;
    final xColors = context.appColors;
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Transform.scale(
          scale: 0.8,
          alignment: Alignment.centerLeft,
          child: Switch(
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            thumbIcon: WidgetStateProperty.resolveWith(
              (state) => Icon(
                Icons.check,
                size: 16,
                color: colorScheme.surface,
              ),
            ),
            value: widget.isActive,
            onChanged: widget.onChanged,
            trackOutlineColor: WidgetStateProperty.all(Colors.transparent),
            inactiveTrackColor: AppColors.grey,
          ),
        ),
        if (widget.title != null) ...[
          Expanded(
            child: Text(
              widget.title!,
              style: context.typography.mediumReg.copyWith(
                color: widget.isActive
                    ? xColors.pPXLight
                    : xColors.blackGreyMedium,
              ),
            ),
          ),
        ],
      ],
    );
  }
}
