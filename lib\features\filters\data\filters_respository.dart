import 'package:buyer_board/core/network/rest_api_client.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/filters/models/new_filter_model.dart';

abstract interface class FiltersRepository {
  Future<BuyerModelResponse> filteredBuyers(NewFilterModel filtersRequestModel);
}

final class FiltersRepositoryImpl implements FiltersRepository {
  final RestAPIClient restAPIClient;

  FiltersRepositoryImpl({required this.restAPIClient});

  @override
  Future<BuyerModelResponse> filteredBuyers(
      NewFilterModel filtersRequestModel) async {
    final response =
        await restAPIClient.filteredBuyers(filterModel: filtersRequestModel);
    return response;
  }
}
