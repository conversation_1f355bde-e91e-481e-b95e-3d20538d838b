import 'dart:io';

import 'package:buyer_board/common/widgets/app_terms_and_policy_widget.dart';
import 'package:buyer_board/common/widgets/common_button.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/resources/text_styles.dart';
import 'package:buyer_board/core/utils/loader.dart';
import 'package:buyer_board/features/auth/presentation/cubit/social_auth_cubit.dart';
import 'package:buyer_board/features/auth/presentation/screens/auth_screen.dart';
import 'package:buyer_board/features/auth/presentation/state/social_auth_state.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class CallToActionSction extends StatelessWidget {
  const CallToActionSction({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<SocialAuthCubit, SocialAuthState>(
      listener: (context, state) {
        state.mapOrNull(
            loading: (state) => Loader.show(),
            socialAuthSuccess: (state) =>
                {Loader.hide(), context.go(PagePath.mainScreen)},
            socialAuthError: (state) => {
                  Loader.hide(),
                  context.showToast(isSuccess: false, message: state.error)
                });
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.center,
          //   children: [
          //     Text(
          //       "Lorem ipsum dolor sit amet ",
          //       style: AppStyles.small.copyWith(color: AppColors.white),
          //     ),
          //     Padding(
          //       padding: const EdgeInsets.symmetric(horizontal: 2),
          //       child: Text(
          //         Strings.termsOfService,
          //         style: AppStyles.smallBold.copyWith(color: AppColors.white),
          //       ),
          //     ),
          //   ],
          // ),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.center,
          //   children: [
          //     Text(
          //       "Rhoncus sit pulvinar ",
          //       style: AppStyles.small.copyWith(color: AppColors.white),
          //     ),
          //     Padding(
          //       padding: const EdgeInsets.symmetric(horizontal: 2),
          //       child: Text(
          //         Strings.privacyPolicy,
          //         style: AppStyles.smallBold.copyWith(color: AppColors.white),
          //       ),
          //     ),
          //   ],
          // ),
          const AppTermsAndPolicyWidget(),
          const SizedBox(height: 4),
          if (Platform.isIOS)
            CommonButton.loginWithApple(
                textColor: context.theme.colorScheme.primaryFixed,
                action: () => context
                    .read<SocialAuthCubit>()
                    .authenticateWithApple(context)),
          CommonButton.loginWithGoogle(
              textColor: context.theme.colorScheme.primaryFixed,
              action: () => context
                  .read<SocialAuthCubit>()
                  .authenticateWithGoogle(context)),
          CommonButton.loginWithEmailPassword(
            action: () => authActionNotifier.value = AuthAction.login,
          ),
          CommonButton.basic(
            label: Strings.signUp,
            action: () => authActionNotifier.value = AuthAction.signup,
            backgroundColor: AppColors.primary,
          ),
        ],
      ),
    );
  }
}
