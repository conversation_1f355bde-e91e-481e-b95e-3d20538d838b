import 'dart:convert';

import 'package:buyer_board/features/menu/presentation/about_buyer_board/state/intro_to_buyer_board_state.dart';
import 'package:buyer_board/features/onboarding/data/onboarding_model.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';


class IntroToBuyerBoardCubit extends Cubit<IntroToBuyerBoardState> {
  IntroToBuyerBoardCubit() : super(const IntroToBuyerBoardState.initial());

  Future<void> initBuyerBoardIntro() async {
    final source = await rootBundle.loadString("assets/json/introBuyerBoard.json");
    final json = jsonDecode(source) as List<dynamic>;
    final onBoardingData =
    json.map((e) => OnboardingModel.fromJson(e)).toList();
    emit(IntroToBuyerBoardState.success(onBoardingData));
  }
}
