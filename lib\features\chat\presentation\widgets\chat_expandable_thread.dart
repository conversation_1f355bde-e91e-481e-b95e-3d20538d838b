import 'package:buyer_board/common/widgets/profile_avatar.dart';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/extensions/date_time_extension.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/core/theme/app_color_extension.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/core/utils/core_utils.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_archive_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_archive_chats_events.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_bloc.dart';
import 'package:buyer_board/features/chat/presentation/bloc/all_chats_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';

class ChatExpandableThread extends StatefulWidget {
  const ChatExpandableThread({
    super.key,
    required this.chatBuyerModel,
    this.isArchiveMode = true,
  });
  final ChatGroupModel chatBuyerModel;
  final bool isArchiveMode;

  @override
  State<ChatExpandableThread> createState() => _ChatExpandableThreadState();
}

class _ChatExpandableThreadState extends State<ChatExpandableThread> {
  bool _expanded = false;
  @override
  Widget build(BuildContext context) {
    final xColors = context.theme.appColors;
    final typography = context.typography;
    final myBuyer = widget.chatBuyerModel.myBuyer;
    final tileBackgroundColor =
        !myBuyer ? xColors.gBlackGLight : xColors.pPXLight;
    final tileForegroundColor =
        !myBuyer ? xColors.whiteBlack : xColors.whitePXDark;
    final fullName =
        '${widget.chatBuyerModel.firstName} ${widget.chatBuyerModel.lastName}';
    return widget.chatBuyerModel.threads.isEmpty
        ? const SizedBox.shrink()
        : Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Theme(
              data: Theme.of(context).copyWith(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
              ),
              child: ListTileTheme(
                tileColor: tileBackgroundColor,
                child: Stack(
                  alignment: AlignmentDirectional.topStart,
                  children: [
                    ExpansionTile(
                      collapsedBackgroundColor: tileBackgroundColor,
                      backgroundColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      collapsedShape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      onExpansionChanged: (value) {
                        setState(() {
                          _expanded = value;
                        });
                      },
                      trailing: AnimatedRotation(
                        turns: _expanded ? 0.5 : 0,
                        duration: const Duration(milliseconds: 250),
                        child: Icon(
                          Icons.arrow_drop_down,
                          size: 30,
                          color: tileForegroundColor,
                        ),
                      ),
                      childrenPadding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 12),
                      expansionAnimationStyle: AnimationStyle(
                        curve: Curves.easeInOut,
                        duration: const Duration(milliseconds: 250),
                      ),
                      tilePadding: const EdgeInsetsDirectional.only(
                        start: 20,
                        end: 16,
                      ),
                      dense: true,
                      title: Text(
                        myBuyer
                            ? fullName
                            : widget.chatBuyerModel.sku.toString(),
                        style: typography.mediumSemi.copyWith(
                          color: tileForegroundColor,
                        ),
                      ),
                      children: [
                        for (final agent in widget.chatBuyerModel.threads)
                          Column(
                            children: [
                              _ThreadItem(
                                isArchiveMode: widget.isArchiveMode,
                                agent: agent,
                                onTap: () {
                                  final extra = (
                                    buyer: widget.chatBuyerModel,
                                    agent: agent
                                  );
                                  context.pushNamed(
                                    PagePath.chatDetailsScreen,
                                    extra: extra,
                                  );
                                },
                              ),
                              spacerH8,
                            ],
                          ),
                      ],
                    ),
                    if (widget.chatBuyerModel.hasNewMessage)
                      _circleBadge(xColors),
                  ],
                ),
              ),
            ),
          );
  }

  PositionedDirectional _circleBadge(AppColorsExtension xColors) {
    return PositionedDirectional(
      start: 5,
      top: 5,
      child: Container(
        height: 10,
        width: 10,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: !widget.chatBuyerModel.myBuyer
                ? xColors.whiteBlack
                : xColors.black,
            width: 1,
          ),
          color: xColors.error,
        ),
      ),
    );
  }
}

class _ThreadItem extends StatefulWidget {
  const _ThreadItem({
    required this.agent,
    required this.onTap,
    required this.isArchiveMode,
  });
  final ChatGroupThreadModel agent;
  final VoidCallback onTap;
  final bool isArchiveMode;

  @override
  State<_ThreadItem> createState() => _ThreadItemState();
}

class _ThreadItemState extends State<_ThreadItem> {
  @override
  Widget build(BuildContext context) {
    final typography = context.typography;
    final xColors = context.appColors;
    final firstName = widget.agent.user?.profile?.firstName;
    final lastName = widget.agent.user?.profile?.lastName;
    final email = widget.agent.user?.profile?.agentEmail ?? 'User';

    final name = ('${firstName ?? ''} ${lastName ?? ''}').trim();
    final avatarUrl = widget.agent.user?.profile?.imageUrl ?? "";

    final firstTwoLetters = ((firstName != null ? firstName[0] : '') +
            (lastName != null ? lastName[0] : ''))
        .toUpperCase();

    final isDeleted = widget.agent.lastMessage?.isDeleted ?? false;
    return Dismissible(
      confirmDismiss: (direction) async {
        return await _archiveChat(context);
      },
      background: Container(
        color: context.theme.appColors.pPXLight,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        alignment: Alignment.centerLeft,
        child: Icon(
          Icons.archive,
          color: context.theme.appColors.whitePXDark,
        ),
      ),
      secondaryBackground: Container(
        color: context.theme.appColors.pPXLight,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        alignment: Alignment.centerRight,
        child: Icon(
          Icons.archive,
          color: context.theme.appColors.whitePXDark,
        ),
      ),
      key: UniqueKey(),
      child: Column(
        children: [
          InkWell(
            onTap: widget.onTap,
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsetsDirectional.only(end: 4),
                  child: CircleAvatar(
                    radius: 4,
                    backgroundColor: widget.agent.hasNewMessage
                        ? xColors.error
                        : Colors.transparent,
                  ),
                ),
                ProfileAvatar(
                  avatarPlaceHolderTitle:
                      firstTwoLetters.isNotEmpty ? firstTwoLetters : 'User',
                  textStyle: typography.largeSemi.copyWith(
                    color: xColors.whitePXDark,
                  ),
                  radius: 28.0,
                  backgroundColor: context.theme.appColors.pPXLight,
                  imageUrl: avatarUrl,
                ),
                spacerW12,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: MediaQuery.sizeOf(context).width * 0.35,
                            child: Text(
                              name.isNotEmpty ? name : email,
                              style: typography.mediumBlack.copyWith(
                                color: xColors.blackWhite,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            (DateTime.tryParse(widget.agent.timeStamp ?? '')!
                                    .toLocal())
                                .formatRelativeDateTime(),
                            style: typography.mediumReg.copyWith(
                              color: xColors.greyM,
                            ),
                          ),
                          spacerW8,
                          Icon(
                            Icons.arrow_forward_ios_outlined,
                            size: 20,
                            color: xColors.greyM,
                          ),
                        ],
                      ),
                      Text(
                        isDeleted
                            ? '(Message deleted)'
                            : widget.agent.lastMessage?.message.isNotEmpty ==
                                    true
                                ? widget.agent.lastMessage!.message
                                : (widget.agent.lastMessage?.attachments
                                            .isNotEmpty ==
                                        true
                                    ? (widget.agent.lastMessage!.attachments
                                            .any((attachment) =>
                                                attachment.type == 'video')
                                        ? 'Video'
                                        : widget.agent.lastMessage!.attachments
                                                .any((attachment) =>
                                                    attachment.type == 'image')
                                            ? 'Photo'
                                            : '')
                                    : ''),
                        style: typography.smallReg.copyWith(
                          color: xColors.greyM,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          spacerH4,
          const Divider(
            indent: 76,
            height: 0,
          )
        ],
      ),
    );
  }

  Future<bool> _archiveChat(BuildContext context) async {
    final confirmed = await CoreUtils.showConfirmationDialog(
      context,
      // title:  'Archive Chat',
      title: widget.isArchiveMode ? 'Archive Chat' : 'Unarchive Chat',
      // content: 'Are you sure you want to archive this chat?',
      content: widget.isArchiveMode
          ? 'Are you sure you want to archive this chat?'
          : 'Are you sure you want to unarchive this chat?',
      // action: 'Archive',
      action: widget.isArchiveMode ? 'Archive' : 'Unarchive',
    );
    if (confirmed && context.mounted) {
      final appPreferences = Injector.resolve<AppPreferences>();
      final userSessionCubit = Injector.resolve<UserSessionCubit>();
      final userId = appPreferences.getUser()?.id ?? userSessionCubit.state?.id;
      if (userId == null) {
        return false;
      }
      if (widget.isArchiveMode) {
        context.read<AllChatsBloc>().add(ArchiveChat(ChatArchivePayload(
            threadId: widget.agent.id ?? -1, userId: userId)));
      } else {
        context.read<AllArchiveChatsBloc>().add(
              UnArchiveChat(
                ChatUnarchivePayload(
                    threadId: widget.agent.id ?? -1, userId: userId),
              ),
            );
      }
      // context.read<AllChatsBloc>().add(ArchiveChat(
      //     ChatArchivePayload(threadId: widget.agent.id ?? -1, userId: userId)));
    } else {
      if (context.mounted) {
        Slidable.of(context)?.close();
      }
    }
    return confirmed;
  }
}
