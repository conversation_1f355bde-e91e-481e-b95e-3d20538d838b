import 'package:flutter/material.dart';

class AppSlideTransition extends StatelessWidget {
  final Widget child;
  final AppSlideTransitionType type;

  const AppSlideTransition({
    Key? key,
    required this.child,
    required this.type,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final offset = switch (type) {
      AppSlideTransitionType.slideUp => const Offset(0, 1),
      AppSlideTransitionType.slideDown => const Offset(0, -1),
      AppSlideTransitionType.slideLeft => const Offset(1, 0),
      AppSlideTransitionType.slideRight => const Offset(-1, 0),
    };
    return AnimatedSwitcher(
      switchOutCurve: Curves.easeInOut,
      duration: const Duration(milliseconds: 300),
      transitionBuilder: (Widget child, Animation<double> animation) {
        final offsetAnimation = Tween<Offset>(
          begin: offset,
          end: Offset.zero,
        ).animate(animation);

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
      layoutBuilder: (Widget? currentChild, List<Widget> previousChildren) {
        return Stack(
          alignment: Alignment.center,
          children: <Widget>[
            ...previousChildren.map((child) {
              final offsetAnimation = Tween<Offset>(
                begin: offset,
                end: Offset.zero,
              ).animate(
                AnimationController(
                  vsync: Scaffold.of(context),
                  duration: const Duration(milliseconds: 300),
                )..forward(),
              );
              return SlideTransition(
                position: offsetAnimation,
                child: child,
              );
            }).toList(),
            if (currentChild != null) currentChild,
          ],
        );
      },
      child: child,
    );
  }
}

enum AppSlideTransitionType {
  slideUp,
  slideDown,
  slideLeft,
  slideRight,
}
