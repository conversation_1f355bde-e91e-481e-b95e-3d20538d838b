import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/add_buyer/domain/repository/buyer_repository.dart';
import 'package:buyer_board/features/favorites/presentation/state/favourite_buyer_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FavouriteBuyersCubit extends Cubit<FavouriteBuyerState> {
  FavouriteBuyersCubit({required this.buyerRepository})
      : super(const FavouriteBuyerState.initial());
  final BuyerRepository buyerRepository;
  void getFavouriteBuyers({bool silent = false}) async {
    if (!silent) {
      emit(const FavouriteBuyerState.loading());
    }
    try {
      final response = await buyerRepository.getFavouriteBuyers();
      if (response.data != null) {
        emit(
          FavouriteBuyerState.success(
            buyers: response.data!,
            message: response.message.body ?? "",
          ),
        );
      } else {
        emit(
          const FavouriteBuyerState.favouriteBuyersError(
            Strings.commonError,
          ),
        );
      }
    } catch (e) {
      emit(
        FavouriteBuyerState.favouriteBuyersError(e.toString()),
      );
    }
  }

  Future<void> toggleFavourite(
      {required int buyerId, required bool isFavourite}) async {
    emit(const FavouriteBuyerState.toggleInProgress());
    try {
      final response = await buyerRepository.favouriteBuyer(
        isFavourite: isFavourite,
        id: buyerId,
      );
      if (response.data != null) {
        emit(
          FavouriteBuyerState.toggleSuccess(
            buyer: response.data!,
            message: response.message.body ?? "",
          ),
        );
      } else {
        emit(
          const FavouriteBuyerState.toggleError(
              "Buyer Could not be marked Favourite. Try Later"),
        );
      }
    } catch (e) {
      emit(
        FavouriteBuyerState.toggleError(e.toString()),
      );
    }
  }
}
