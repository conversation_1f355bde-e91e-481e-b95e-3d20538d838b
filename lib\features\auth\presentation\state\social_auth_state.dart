import 'package:freezed_annotation/freezed_annotation.dart';
part 'social_auth_state.freezed.dart';

@freezed
class SocialAuthState with _$SocialAuthState {
  const factory SocialAuthState.initial() = initial;
  const factory SocialAuthState.loading() = loading;
  const factory SocialAuthState.socialAuthSuccess(dynamic data) =
      googleAuthSuccess;
  const factory SocialAuthState.socialAuthError(String? error) =
      socialAuthError;
}
