import 'package:buyer_board/features/menu/data/models/faq_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'faq_screen_state.freezed.dart';

@freezed
class FaqScreenState with _$FaqScreenState {
  const factory FaqScreenState.initial() = initial;
  const factory FaqScreenState.loading() = loading;
  const factory FaqScreenState.success(FaqModel faq) = success;
  const factory FaqScreenState.faqError(String? error) =
  faqError;
}