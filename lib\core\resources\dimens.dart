import 'package:flutter/material.dart';

class Dimensions {
  Dimensions._();

  static const materialPadding = 16.0;
  static const materialPadding_22 = 22.0;
  static const materialPadding_36 = 36.0;
  static const materialPadding_34 = 34.0;
  static const materialRadius = 15.0;
  static const materialPadding_2x = 32.0;
  static const materialPadding_3x = 48.0;
  static const materialPadding_4x = 64.0;
  static const materialPadding_5x = 80.0;

  static const materialSheetRadius = 35.0;
  static const padding_5 = 5.0;
  static const padding_4 = 4.0;
  static const padding_2 = 2.0;
  static const padding_6 = 6.0;
  static const padding_8 = 8.0;
  static const padding_10 = 10.0;
  static const padding_20 = 20.0;
  static const padding_12 = 12.0;
  static const padding_14 = 14.0;
  static const padding_24 = 24.0;
  static const padding_26 = 26.0;
  static const padding_28 = 28.0;
  static const createAccountButtonPadding = 200.0;
  static const padding_56 = 56.0;
  static const padding_60 = 60.0;
  static const padding_54 = 54.0;
  static const padding_43 = 43.0;
  static const padding_50 = 50.0;
  static const padding_78 = 78.0;
  static const homeBarChartHeight = 300.0;
  static const seeAllRestaurantsAppBarHeight = 120.0;
  static const bottomButtonPaddingIos = 40.0;
  static const padding_44 = 44.0;
}

const spacerH2 = SizedBox(height: 2);
const spacerH4 = SizedBox(height: 4);
const spacerH8 = SizedBox(height: 8);
const spacerH12 = SizedBox(height: 12);
const spacerH16 = SizedBox(height: 16);
const spacerH20 = SizedBox(height: 20);
const spacerH24 = SizedBox(height: 24);
const spacerH28 = SizedBox(height: 28);
const spacerH32 = SizedBox(height: 32);
const spacerH36 = SizedBox(height: 36);
const spacerH40 = SizedBox(height: 40);
const spacerH44 = SizedBox(height: 44);
const spacerH48 = SizedBox(height: 48);
const spacerH52 = SizedBox(height: 52);
const spacerH56 = SizedBox(height: 56);
const spacerH60 = SizedBox(height: 60);
const spacerH64 = SizedBox(height: 64);
const spacerH68 = SizedBox(height: 68);
const spacerH72 = SizedBox(height: 72);

const spacerW2 = SizedBox(width: 2);
const spacerW4 = SizedBox(width: 4);
const spacerW8 = SizedBox(width: 8);
const spacerW12 = SizedBox(width: 12);
const spacerW16 = SizedBox(width: 16);
const spacerW20 = SizedBox(width: 20);
const spacerW24 = SizedBox(width: 24);
const spacerW28 = SizedBox(width: 28);
const spacerW32 = SizedBox(width: 32);
const spacerW36 = SizedBox(width: 36);
const spacerW40 = SizedBox(width: 40);
const spacerW44 = SizedBox(width: 44);
const spacerW48 = SizedBox(width: 48);
const spacerW52 = SizedBox(width: 52);
const spacerW56 = SizedBox(width: 56);
const spacerW60 = SizedBox(width: 60);
const spacerW64 = SizedBox(width: 64);
const spacerW68 = SizedBox(width: 68);
const spacerW72 = SizedBox(width: 72);
