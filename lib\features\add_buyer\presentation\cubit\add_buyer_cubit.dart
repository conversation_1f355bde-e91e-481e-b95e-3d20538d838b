import 'dart:developer';

import 'package:buyer_board/core/network/interceptors/error_handler_interceptor.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_info.dart';
import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/add_buyer/domain/repository/buyer_repository.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/buyer_info_cubit.dart';
import 'package:buyer_board/features/add_buyer/presentation/states/add_buyer_state.dart';
import 'package:buyer_board/features/profile/domain/repository/user_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../common/utils/image_utils.dart';

class AddBuyerCubit extends Cubit<AddBuyerState> {
  AddBuyerCubit({required this.buerRepository, required this.userRepository})
      : super(const AddBuyerState.initial());
  final BuyerRepository buerRepository;
  final UserRepository userRepository;
  String? selectedImagePath;
  Future<String?> selectProfileImage() async {
    selectedImagePath = await ImageUtils().picAndCropImageFromGallery();
    return selectedImagePath;
  }

  Future<void> addBuyer({required BuyerInfo buyerInfo, int? id}) async {
    emit(const AddBuyerState.loading());
    String? uploadedImage;
    if (selectedImagePath != null) {
      uploadedImage = await ImageUtils().uploadImage(selectedImagePath!);
    }
    try {
      final response = id != null
          ? await buerRepository.editBuyer(
              id: id,
              buyer: BuyerModel(
                imageUrl: uploadedImage,
                firstName: buyerInfo.firstName,
                lastName: buyerInfo.lastName,
                buyersAlias: buyerInfo.alias,
                email: buyerInfo.emailAddress,
                primaryPhoneNumber: buyerInfo.primaryPhoneNumber,
                optionalPhoneNumber: buyerInfo.optionalPhoneNumber,
                buyerLocationsOfInterest: buyerInfo.buyerLocationsOfInterest,
                buyerNeeds: BuyerNeeds(
                  purchaseType: purchaseTypeNotifier.value,
                  propertyType: propertyTypeNotifier.value,
                  financialStatus: financialStatusNotifier.value,
                  budget: buyerInfo.budget,
                  minBedrooms: buyerInfo.bedroomCount,
                  minBathrooms: buyerInfo.bathroomCount,
                  minArea: buyerInfo.area,
                ),
                additionalRequests: buyerInfo.additionalRequests,
                buyerExpirationDate: buyerInfo.buyerExpirationDate,
              ))
          : await buerRepository.addBuyer(
              buyer: BuyerModel(
              imageUrl: uploadedImage,
              firstName: buyerInfo.firstName,
              lastName: buyerInfo.lastName,
              buyersAlias: buyerInfo.alias,
              email: buyerInfo.emailAddress,
              primaryPhoneNumber: buyerInfo.primaryPhoneNumber,
              optionalPhoneNumber: buyerInfo.optionalPhoneNumber,
              buyerLocationsOfInterest: buyerInfo.buyerLocationsOfInterest,
              buyerNeeds: BuyerNeeds(
                purchaseType: purchaseTypeNotifier.value,
                propertyType: propertyTypeNotifier.value,
                financialStatus: financialStatusNotifier.value,
                budget: buyerInfo.budget,
                minBedrooms: buyerInfo.bedroomCount,
                minBathrooms: buyerInfo.bathroomCount,
                minArea: buyerInfo.area,
                // timeline: timelineNotifier.value.name,
              ),
              additionalRequests: buyerInfo.additionalRequests,
              buyerExpirationDate: buyerInfo.buyerExpirationDate,
            ));
      if (response.data != null) {
        emit(
          AddBuyerState.success(
            buyer: response.data!,
            message: response.message.body ?? "",
          ),
        );
      }
    } on ErrorObjectException catch (e) {
      String errorMessage;
      try {
        final errorData = e.errorObject;
        log(errorData.toString());
        if (errorData != null && errorData.isNotEmpty) {
          final firstErrorKey = errorData.keys.first;
          final firstErrorMessage = errorData[firstErrorKey];
          if (firstErrorMessage is List) {
            errorMessage = firstErrorMessage.first;
          } else {
            errorMessage = firstErrorMessage.toString();
          }
        } else {
          errorMessage = "An unexpected error occurred.";
        }
      } catch (_) {
        errorMessage =
            "An error occurred. Please check your input and try again.";
      }
      emit(AddBuyerState.addBuyerError(errorMessage));
    } catch (e) {
      emit(const AddBuyerState.addBuyerError(
          "An unknown error occurred. Please try again."));
    }
  }
}
