// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_with_email_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LoginWithEmailState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message, String route)
        success,
    required TResult Function(AuthRequest authRequest) localAuthSuccess,
    required TResult Function(String message, String route) rememberMe,
    required TResult Function(String? error) loginError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message, String route)? success,
    TResult? Function(AuthRequest authRequest)? localAuthSuccess,
    TResult? Function(String message, String route)? rememberMe,
    TResult? Function(String? error)? loginError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message, String route)? success,
    TResult Function(AuthRequest authRequest)? localAuthSuccess,
    TResult Function(String message, String route)? rememberMe,
    TResult Function(String? error)? loginError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(localAuthSuccess value) localAuthSuccess,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(loginError value) loginError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(localAuthSuccess value)? localAuthSuccess,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(loginError value)? loginError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(localAuthSuccess value)? localAuthSuccess,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(loginError value)? loginError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginWithEmailStateCopyWith<$Res> {
  factory $LoginWithEmailStateCopyWith(
          LoginWithEmailState value, $Res Function(LoginWithEmailState) then) =
      _$LoginWithEmailStateCopyWithImpl<$Res, LoginWithEmailState>;
}

/// @nodoc
class _$LoginWithEmailStateCopyWithImpl<$Res, $Val extends LoginWithEmailState>
    implements $LoginWithEmailStateCopyWith<$Res> {
  _$LoginWithEmailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$LoginWithEmailStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'LoginWithEmailState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message, String route)
        success,
    required TResult Function(AuthRequest authRequest) localAuthSuccess,
    required TResult Function(String message, String route) rememberMe,
    required TResult Function(String? error) loginError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message, String route)? success,
    TResult? Function(AuthRequest authRequest)? localAuthSuccess,
    TResult? Function(String message, String route)? rememberMe,
    TResult? Function(String? error)? loginError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message, String route)? success,
    TResult Function(AuthRequest authRequest)? localAuthSuccess,
    TResult Function(String message, String route)? rememberMe,
    TResult Function(String? error)? loginError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(localAuthSuccess value) localAuthSuccess,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(loginError value) loginError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(localAuthSuccess value)? localAuthSuccess,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(loginError value)? loginError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(localAuthSuccess value)? localAuthSuccess,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(loginError value)? loginError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements LoginWithEmailState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$loadingImplCopyWith<$Res> {
  factory _$$loadingImplCopyWith(
          _$loadingImpl value, $Res Function(_$loadingImpl) then) =
      __$$loadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$loadingImplCopyWithImpl<$Res>
    extends _$LoginWithEmailStateCopyWithImpl<$Res, _$loadingImpl>
    implements _$$loadingImplCopyWith<$Res> {
  __$$loadingImplCopyWithImpl(
      _$loadingImpl _value, $Res Function(_$loadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$loadingImpl implements loading {
  const _$loadingImpl();

  @override
  String toString() {
    return 'LoginWithEmailState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$loadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message, String route)
        success,
    required TResult Function(AuthRequest authRequest) localAuthSuccess,
    required TResult Function(String message, String route) rememberMe,
    required TResult Function(String? error) loginError,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message, String route)? success,
    TResult? Function(AuthRequest authRequest)? localAuthSuccess,
    TResult? Function(String message, String route)? rememberMe,
    TResult? Function(String? error)? loginError,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message, String route)? success,
    TResult Function(AuthRequest authRequest)? localAuthSuccess,
    TResult Function(String message, String route)? rememberMe,
    TResult Function(String? error)? loginError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(localAuthSuccess value) localAuthSuccess,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(loginError value) loginError,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(localAuthSuccess value)? localAuthSuccess,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(loginError value)? loginError,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(localAuthSuccess value)? localAuthSuccess,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(loginError value)? loginError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class loading implements LoginWithEmailState {
  const factory loading() = _$loadingImpl;
}

/// @nodoc
abstract class _$$successImplCopyWith<$Res> {
  factory _$$successImplCopyWith(
          _$successImpl value, $Res Function(_$successImpl) then) =
      __$$successImplCopyWithImpl<$Res>;
  @useResult
  $Res call({User authResponse, String message, String route});
}

/// @nodoc
class __$$successImplCopyWithImpl<$Res>
    extends _$LoginWithEmailStateCopyWithImpl<$Res, _$successImpl>
    implements _$$successImplCopyWith<$Res> {
  __$$successImplCopyWithImpl(
      _$successImpl _value, $Res Function(_$successImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authResponse = null,
    Object? message = null,
    Object? route = null,
  }) {
    return _then(_$successImpl(
      null == authResponse
          ? _value.authResponse
          : authResponse // ignore: cast_nullable_to_non_nullable
              as User,
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$successImpl implements success {
  const _$successImpl(this.authResponse, this.message, this.route);

  @override
  final User authResponse;
  @override
  final String message;
  @override
  final String route;

  @override
  String toString() {
    return 'LoginWithEmailState.success(authResponse: $authResponse, message: $message, route: $route)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$successImpl &&
            (identical(other.authResponse, authResponse) ||
                other.authResponse == authResponse) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.route, route) || other.route == route));
  }

  @override
  int get hashCode => Object.hash(runtimeType, authResponse, message, route);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      __$$successImplCopyWithImpl<_$successImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message, String route)
        success,
    required TResult Function(AuthRequest authRequest) localAuthSuccess,
    required TResult Function(String message, String route) rememberMe,
    required TResult Function(String? error) loginError,
  }) {
    return success(authResponse, message, route);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message, String route)? success,
    TResult? Function(AuthRequest authRequest)? localAuthSuccess,
    TResult? Function(String message, String route)? rememberMe,
    TResult? Function(String? error)? loginError,
  }) {
    return success?.call(authResponse, message, route);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message, String route)? success,
    TResult Function(AuthRequest authRequest)? localAuthSuccess,
    TResult Function(String message, String route)? rememberMe,
    TResult Function(String? error)? loginError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(authResponse, message, route);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(localAuthSuccess value) localAuthSuccess,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(loginError value) loginError,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(localAuthSuccess value)? localAuthSuccess,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(loginError value)? loginError,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(localAuthSuccess value)? localAuthSuccess,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(loginError value)? loginError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class success implements LoginWithEmailState {
  const factory success(
          final User authResponse, final String message, final String route) =
      _$successImpl;

  User get authResponse;
  String get message;
  String get route;

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$localAuthSuccessImplCopyWith<$Res> {
  factory _$$localAuthSuccessImplCopyWith(_$localAuthSuccessImpl value,
          $Res Function(_$localAuthSuccessImpl) then) =
      __$$localAuthSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AuthRequest authRequest});
}

/// @nodoc
class __$$localAuthSuccessImplCopyWithImpl<$Res>
    extends _$LoginWithEmailStateCopyWithImpl<$Res, _$localAuthSuccessImpl>
    implements _$$localAuthSuccessImplCopyWith<$Res> {
  __$$localAuthSuccessImplCopyWithImpl(_$localAuthSuccessImpl _value,
      $Res Function(_$localAuthSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authRequest = null,
  }) {
    return _then(_$localAuthSuccessImpl(
      null == authRequest
          ? _value.authRequest
          : authRequest // ignore: cast_nullable_to_non_nullable
              as AuthRequest,
    ));
  }
}

/// @nodoc

class _$localAuthSuccessImpl implements localAuthSuccess {
  const _$localAuthSuccessImpl(this.authRequest);

  @override
  final AuthRequest authRequest;

  @override
  String toString() {
    return 'LoginWithEmailState.localAuthSuccess(authRequest: $authRequest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$localAuthSuccessImpl &&
            (identical(other.authRequest, authRequest) ||
                other.authRequest == authRequest));
  }

  @override
  int get hashCode => Object.hash(runtimeType, authRequest);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$localAuthSuccessImplCopyWith<_$localAuthSuccessImpl> get copyWith =>
      __$$localAuthSuccessImplCopyWithImpl<_$localAuthSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message, String route)
        success,
    required TResult Function(AuthRequest authRequest) localAuthSuccess,
    required TResult Function(String message, String route) rememberMe,
    required TResult Function(String? error) loginError,
  }) {
    return localAuthSuccess(authRequest);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message, String route)? success,
    TResult? Function(AuthRequest authRequest)? localAuthSuccess,
    TResult? Function(String message, String route)? rememberMe,
    TResult? Function(String? error)? loginError,
  }) {
    return localAuthSuccess?.call(authRequest);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message, String route)? success,
    TResult Function(AuthRequest authRequest)? localAuthSuccess,
    TResult Function(String message, String route)? rememberMe,
    TResult Function(String? error)? loginError,
    required TResult orElse(),
  }) {
    if (localAuthSuccess != null) {
      return localAuthSuccess(authRequest);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(localAuthSuccess value) localAuthSuccess,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(loginError value) loginError,
  }) {
    return localAuthSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(localAuthSuccess value)? localAuthSuccess,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(loginError value)? loginError,
  }) {
    return localAuthSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(localAuthSuccess value)? localAuthSuccess,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(loginError value)? loginError,
    required TResult orElse(),
  }) {
    if (localAuthSuccess != null) {
      return localAuthSuccess(this);
    }
    return orElse();
  }
}

abstract class localAuthSuccess implements LoginWithEmailState {
  const factory localAuthSuccess(final AuthRequest authRequest) =
      _$localAuthSuccessImpl;

  AuthRequest get authRequest;

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$localAuthSuccessImplCopyWith<_$localAuthSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$rememberMeImplCopyWith<$Res> {
  factory _$$rememberMeImplCopyWith(
          _$rememberMeImpl value, $Res Function(_$rememberMeImpl) then) =
      __$$rememberMeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, String route});
}

/// @nodoc
class __$$rememberMeImplCopyWithImpl<$Res>
    extends _$LoginWithEmailStateCopyWithImpl<$Res, _$rememberMeImpl>
    implements _$$rememberMeImplCopyWith<$Res> {
  __$$rememberMeImplCopyWithImpl(
      _$rememberMeImpl _value, $Res Function(_$rememberMeImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? route = null,
  }) {
    return _then(_$rememberMeImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      null == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$rememberMeImpl implements rememberMe {
  const _$rememberMeImpl(this.message, this.route);

  @override
  final String message;
  @override
  final String route;

  @override
  String toString() {
    return 'LoginWithEmailState.rememberMe(message: $message, route: $route)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$rememberMeImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.route, route) || other.route == route));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, route);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$rememberMeImplCopyWith<_$rememberMeImpl> get copyWith =>
      __$$rememberMeImplCopyWithImpl<_$rememberMeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message, String route)
        success,
    required TResult Function(AuthRequest authRequest) localAuthSuccess,
    required TResult Function(String message, String route) rememberMe,
    required TResult Function(String? error) loginError,
  }) {
    return rememberMe(message, route);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message, String route)? success,
    TResult? Function(AuthRequest authRequest)? localAuthSuccess,
    TResult? Function(String message, String route)? rememberMe,
    TResult? Function(String? error)? loginError,
  }) {
    return rememberMe?.call(message, route);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message, String route)? success,
    TResult Function(AuthRequest authRequest)? localAuthSuccess,
    TResult Function(String message, String route)? rememberMe,
    TResult Function(String? error)? loginError,
    required TResult orElse(),
  }) {
    if (rememberMe != null) {
      return rememberMe(message, route);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(localAuthSuccess value) localAuthSuccess,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(loginError value) loginError,
  }) {
    return rememberMe(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(localAuthSuccess value)? localAuthSuccess,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(loginError value)? loginError,
  }) {
    return rememberMe?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(localAuthSuccess value)? localAuthSuccess,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(loginError value)? loginError,
    required TResult orElse(),
  }) {
    if (rememberMe != null) {
      return rememberMe(this);
    }
    return orElse();
  }
}

abstract class rememberMe implements LoginWithEmailState {
  const factory rememberMe(final String message, final String route) =
      _$rememberMeImpl;

  String get message;
  String get route;

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$rememberMeImplCopyWith<_$rememberMeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$loginErrorImplCopyWith<$Res> {
  factory _$$loginErrorImplCopyWith(
          _$loginErrorImpl value, $Res Function(_$loginErrorImpl) then) =
      __$$loginErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$loginErrorImplCopyWithImpl<$Res>
    extends _$LoginWithEmailStateCopyWithImpl<$Res, _$loginErrorImpl>
    implements _$$loginErrorImplCopyWith<$Res> {
  __$$loginErrorImplCopyWithImpl(
      _$loginErrorImpl _value, $Res Function(_$loginErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$loginErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$loginErrorImpl implements loginError {
  const _$loginErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'LoginWithEmailState.loginError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$loginErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$loginErrorImplCopyWith<_$loginErrorImpl> get copyWith =>
      __$$loginErrorImplCopyWithImpl<_$loginErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message, String route)
        success,
    required TResult Function(AuthRequest authRequest) localAuthSuccess,
    required TResult Function(String message, String route) rememberMe,
    required TResult Function(String? error) loginError,
  }) {
    return loginError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message, String route)? success,
    TResult? Function(AuthRequest authRequest)? localAuthSuccess,
    TResult? Function(String message, String route)? rememberMe,
    TResult? Function(String? error)? loginError,
  }) {
    return loginError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message, String route)? success,
    TResult Function(AuthRequest authRequest)? localAuthSuccess,
    TResult Function(String message, String route)? rememberMe,
    TResult Function(String? error)? loginError,
    required TResult orElse(),
  }) {
    if (loginError != null) {
      return loginError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(localAuthSuccess value) localAuthSuccess,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(loginError value) loginError,
  }) {
    return loginError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(localAuthSuccess value)? localAuthSuccess,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(loginError value)? loginError,
  }) {
    return loginError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(localAuthSuccess value)? localAuthSuccess,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(loginError value)? loginError,
    required TResult orElse(),
  }) {
    if (loginError != null) {
      return loginError(this);
    }
    return orElse();
  }
}

abstract class loginError implements LoginWithEmailState {
  const factory loginError(final String? error) = _$loginErrorImpl;

  String? get error;

  /// Create a copy of LoginWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$loginErrorImplCopyWith<_$loginErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
