import 'dart:async';
import 'dart:math';
import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/errors/sockets_exceptions.dart';
import 'package:buyer_board/core/network/network_info.dart';
import 'package:logger/web.dart';
import 'package:rxdart/rxdart.dart'; // Import RxDart
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

abstract class WebSocketService {
  connect(String token);
  void ensureConnected();
  WebSocketChannel? get channel;
  Future<void> sendMessage(dynamic message);
  Stream<dynamic> get stream;
  void disconnect();
}

class FlutterWebSocketService implements WebSocketService {
  WebSocketChannel? _channel;
  String? _token;
  BehaviorSubject<dynamic>? _messageSubject;
  Timer? _reconnectTimer; // Timer for handling reconnections
  bool _isReconnecting = false; // To prevent multiple reconnects simultaneously
  int _reconnectAttempts = 0; // Track number of reconnection attempts
  final int _maxReconnectAttempts =
      5; // Limit the number of reconnection attempts

  final NetworkInfo _networkInfo = Injector.resolve<NetworkInfo>();

  FlutterWebSocketService() {
    _initializeSubject();
  }

  void _initializeSubject() {
    _messageSubject = BehaviorSubject<dynamic>();
  }

  @override
  connect(String token) async {
    _token = token;
    try {
      if (_channel == null) {
        final appConfig = Injector.resolve<AppConfig>();
        final url = Uri.parse(
            '${appConfig.socketBaseUrl}/api/socket/websocket?token=$token');
        Logger().i('Attempting to connect to $url');

        _channel = IOWebSocketChannel.connect(
          url,
          pingInterval: const Duration(seconds: 50),
          headers: {
            'Connection': 'Upgrade',
            'Upgrade': 'websocket',
          },
        );

        if (_channel != null) {
          Logger().i('Connected to $url');
          _reconnectAttempts = 0;
          _channel!.stream.listen(
            (message) {
              _messageSubject?.add(message);
            },
            onError: (error) {
              Logger().e('WebSocket error: $error');
              _messageSubject?.addError(UnexpectedException(
                'Error in WebSocket stream: ${error.toString()}',
              ));
              _attemptReconnect();
            },
            onDone: () {
              Logger().i('WebSocket connection closed');
              _messageSubject?.close();
              _attemptReconnect();
            },
            cancelOnError: true,
          );
        }
      }
    } catch (e) {
      Logger().e('Failed to connect: ${e.toString()}');
      _attemptReconnect();
      throw ConnectionException('Failed to connect: ${e.toString()}');
    }
  }

  void _attemptReconnect() {
    if (_isReconnecting) {
      return;
    }
    _isReconnecting = true;

    if (_reconnectAttempts >= _maxReconnectAttempts) {
      Logger().e('Max reconnection attempts reached.');
      _messageSubject
          ?.addError(ConnectionException('Max reconnection attempts reached'));
      return;
    }

    _reconnectTimer = Timer(
        Duration(seconds: min(5 * (_reconnectAttempts + 1), 30)), () async {
      _reconnectAttempts++;
      Logger().i('Attempting to reconnect... Attempt $_reconnectAttempts');
      try {
        await connect(_token!);
        _isReconnecting = false;
      } catch (e) {
        Logger().e('Reconnection failed: ${e.toString()}');
        _isReconnecting = false;
        _attemptReconnect();
      }
    });
  }

  @override
  void ensureConnected() {
    if (_channel == null && _token != null) {
      connect(_token!);
    }
  }

  @override
  WebSocketChannel? get channel {
    ensureConnected();
    if (_channel == null) {
      throw ConnectionException('WebSocket channel is not established.');
    }
    return _channel;
  }

  @override
  Future<void> sendMessage(dynamic message) async {
    if (!(await _networkInfo.isConnected)) {
      throw MessageSendException('No internet connection.');
    }
    try {
      ensureConnected();

      if (_channel != null) {
        final completer = Completer<void>();

        // Send the message
        _channel!.sink.add(message);

        // Start a timeout to catch any delays in sending
        final timer = Timer(const Duration(seconds: 5), () {
          if (!completer.isCompleted) {
            completer.completeError(
              MessageSendException('Failed to send message: Timeout exceeded'),
            );
          }
        });

        // Listen to the WebSocket stream for an acknowledgment or response
        _messageSubject!.stream.firstWhere((response) {
          return isValidMessageResponse(response);
        }).then((_) {
          if (!completer.isCompleted) {
            completer.complete();
          }
        }).catchError((error) {
          if (!completer.isCompleted) {
            completer.completeError(
              MessageSendException(
                  'Failed to send message: ${error.toString()}'),
            );
          }
        }).whenComplete(() {
          timer.cancel(); // Cancel the timer on successful completion or error
        });

        return completer.future;
      } else {
        throw ConnectionException('WebSocket channel is not available.');
      }
    } catch (e) {
      throw MessageSendException('Failed to send message: ${e.toString()}');
    }
  }

  bool isValidMessageResponse(dynamic response) {
    // Implement logic here to check if the response from the server indicates success.
    // This could be based on message ID, type, or any protocol you are using.
    // return response != null &&
    //     response is Map &&
    //     response['status'] == 'success';
    return true;
  }

  @override
  Stream<dynamic> get stream {
    ensureConnected();
    if (_channel == null) {
      throw ConnectionException('WebSocket channel is not established.');
    }
    return _messageSubject!.stream; // Return the BehaviorSubject's stream
  }

  @override
  void disconnect() {
    try {
      _reconnectTimer?.cancel(); // Cancel any ongoing reconnection attempts
      _isReconnecting = false; // Reset reconnection status
      _channel?.sink.close();
      _channel = null;
      _messageSubject?.close(); // Close the subject
      _initializeSubject(); // Re-initialize the subject for future connections
    } catch (e) {
      throw DisconnectionException('Failed to disconnect: ${e.toString()}');
    }
  }
}
