import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'buyer_info.g.dart';

@JsonSerializable(
  fieldRename: FieldRename.snake,
  explicitToJson: true,
)
class BuyerInfo {
  BuyerInfo({
    this.firstName,
    this.lastName,
    this.alias,
    this.emailAddress,
    this.primaryPhoneNumber,
    this.optionalPhoneNumber,
    this.buyerLocationsOfInterest = const [],
    this.budget,
    this.bedroomCount,
    this.bathroomCount,
    this.area,
    this.additionalRequests = const [],
    this.buyerExpirationDate,
    this.timeOffset,
  });
  BuyerInfo.initial(
      {this.additionalRequests = const [],
      this.buyerLocationsOfInterest = const []});
  String? firstName;
  String? lastName;
  String? alias;
  int? timeOffset;
  String? emailAddress;
  String? primaryPhoneNumber;
  String? optionalPhoneNumber;
  List<String> buyerLocationsOfInterest;
  String? budget;
  double? bedroomCount;
  double? bathroomCount;
  double? area;
  List<String> additionalRequests;
  DateTime? buyerExpirationDate;
  factory BuyerInfo.fromJson(Map<String, dynamic> json) =>
      _$BuyerInfoFromJson(json);
  Map<String, dynamic> toJson() => _$BuyerInfoToJson(this);

  factory BuyerInfo.fromBuyerModel(BuyerModel buyerModel) {
    return BuyerInfo(
      firstName: buyerModel.firstName,
      timeOffset: buyerModel.timeOffset,
      lastName: buyerModel.lastName,
      alias: buyerModel.buyersAlias,
      emailAddress: buyerModel.email,
      primaryPhoneNumber: buyerModel.primaryPhoneNumber,
      optionalPhoneNumber: buyerModel.optionalPhoneNumber,
      buyerLocationsOfInterest: buyerModel.buyerLocationsOfInterest,
      budget: buyerModel.buyerNeeds?.budget,
      bedroomCount: buyerModel.buyerNeeds?.minBedrooms,
      bathroomCount: buyerModel.buyerNeeds?.minBathrooms,
      area: buyerModel.buyerNeeds?.minArea,
      additionalRequests: buyerModel.additionalRequests,
      buyerExpirationDate: buyerModel.buyerExpirationDate,
    );
  }

  BuyerInfo copyWith({
    String? firstName,
    int? timeOffset,
    String? lastName,
    String? alias,
    String? emailAddress,
    String? primaryPhoneNumber,
    String? optionalPhoneNumber,
    List<String>? buyerLocationsOfInterest,
    String? budget,
    double? bedroomCount,
    double? bathroomCount,
    double? area,
    List<String>? additionalRequests,
    DateTime? buyerExpirationDate,
  }) {
    return BuyerInfo(
      firstName: firstName ?? this.firstName,
      timeOffset: timeOffset ?? this.timeOffset,
      lastName: lastName ?? this.lastName,
      alias: alias ?? this.alias,
      emailAddress: emailAddress ?? this.emailAddress,
      primaryPhoneNumber: primaryPhoneNumber ?? this.primaryPhoneNumber,
      optionalPhoneNumber: optionalPhoneNumber ?? this.optionalPhoneNumber,
      buyerLocationsOfInterest:
          buyerLocationsOfInterest ?? this.buyerLocationsOfInterest,
      budget: budget ?? this.budget,
      bedroomCount: bedroomCount ?? this.bedroomCount,
      bathroomCount: bathroomCount ?? this.bathroomCount,
      area: area ?? this.area,
      additionalRequests: additionalRequests ?? this.additionalRequests,
      buyerExpirationDate: buyerExpirationDate ?? this.buyerExpirationDate,
    );
  }
}
