import 'package:buyer_board/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequireProfileScreen extends StatefulWidget {
  const RequireProfileScreen({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  State<RequireProfileScreen> createState() => _RequireProfileScreenState();
}

class _RequireProfileScreenState extends State<RequireProfileScreen> {
  @override
  void initState() {
    super.initState();
    context.read<ProfileCubit>().getUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    
    return widget.child;
  }
}
