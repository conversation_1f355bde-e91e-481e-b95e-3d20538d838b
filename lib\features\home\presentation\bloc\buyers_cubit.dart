import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:buyer_board/features/add_buyer/domain/repository/buyer_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BuyerCubit extends Cubit<BuyerCubitState> {
  BuyerCubit({
    required this.buyerRepository,
  }) : super(BuyerCubitInitial());

  final BuyerRepository buyerRepository;

  void fetchBuyer(int id) async {
    emit(BuyerCubitLoading());
    try {
      final response = await buyerRepository.getBuyer(id);
      emit(BuyerCubitLoaded(response.data!));
    } catch (e) {
      emit(BuyerCubitError(e.toString()));
    }
  }
}

sealed class BuyerCubitState {}

final class BuyerCubitInitial extends BuyerCubitState {}

final class BuyerCubitLoading extends BuyerCubitState {}

final class BuyerCubitLoaded extends BuyerCubitState {
  final BuyerModel buyers;
  BuyerCubitLoaded(this.buyers);
}

final class BuyerCubitError extends BuyerCubitState {
  final String error;
  BuyerCubitError(this.error);
}
