import 'package:buyer_board/features/add_buyer/data/models/buyer_model.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'add_buyer_state.freezed.dart';

@freezed
class AddBuyerState with _$AddBuyerState {
  const factory AddBuyerState.initial() = initial;
  const factory AddBuyerState.loading() = loading;
  const factory AddBuyerState.success(
      {required BuyerModel buyer, required String message}) = success;
  const factory AddBuyerState.addBuyerError(String? error) = addBuyerError;
  const factory AddBuyerState.uploadBuyerImage({required String imageUrl, String? message}) = uploadBuyerImage;
}
