import 'dart:convert';

import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/chat/data/datasources/chat_websocket_service.dart';
import 'package:buyer_board/features/chat/data/models/chat_buyer_model.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/domain/entities/chat_event_enum.dart';
import 'package:buyer_board/features/chat/domain/repositories/archived_chat_repository.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_socket_connection_cubit.dart';

final class ArchivedChatRepositoryImpl implements ArchivedChatRepository {
  final WebSocketService _webSocketService;
  final AppPreferences _appPreferences;
  final UserSessionCubit _userSessionCubit;
  ArchivedChatRepositoryImpl(this._webSocketService)
      : _appPreferences = Injector.resolve<AppPreferences>(),
        _userSessionCubit = Injector.resolve<UserSessionCubit>();

  /// Sends the payload to the server
  Future<void> _sendPayload(ChatPayload payload) async {
    print("""
    Payload: ${payload.toJson()}
""");
    _webSocketService.sendMessage(jsonEncode(payload.toJson()));
  }

  /// This method is used to map the event received from the server
  /// to the enum value
  ChatEventType _mapEvent(String event) {
    return switch (event) {
      'phx_reply' => ChatEventType.phxReply,
      'new_message' => ChatEventType.newMessage,
      'edit_message' => ChatEventType.editMessage,
      'delete_message' => ChatEventType.deleteMessage,
      'archive_chat' => ChatEventType.archiveChat,
      'unarchive_chat' => ChatEventType.unarchiveChat,
      _ => ChatEventType.other,
    };
  }

  /// UnArchives a chat between two people
  /// Note that this method also gets subscribed to the WebSocket
  /// However, We don't need to listen to the response here
  /// The source of truth is [getAllChats] method
  /// which is used to listen to the main chat list
  @override
  Future<void> unarchiveChat(ChatPayload payload) async {
    _sendPayload(payload);
  }

  @override
  Stream<({ChatEventType event, List<ChatGroupModel> data})>
      getArchivedChats() async* {
    final userId = _appPreferences.getUser()?.id ?? _userSessionCubit.state?.id;
    if (userId == null) {
      throw ChatSocketConnectionError('User ID not found');
    }
    _sendPayload(GetAllArchiveChatsPayload(userId: userId));
    await for (final message in _webSocketService.stream) {
      final decodedResponse = jsonDecode(message);
      final event = _mapEvent(decodedResponse['event']);

      final List response = switch (event) {
        ChatEventType.phxReply =>
          decodedResponse['payload']['response'] as List<dynamic>,
        ChatEventType.archiveChat || ChatEventType.unarchiveChat =>
          decodedResponse['payload']['archived_threads'] as List<dynamic>,
        
        _ => [],
      };
      final chats = response.map((e) => ChatGroupModel.fromJson(e)).toList();
      yield (event: event, data: chats);
    }
  }

  /// This method is used to initialize the WebSocket connection
  /// It uses the token from the user session to establish the connection
  /// If the token is not found, it throws an error
  @override
  void initializeConnection() {
    final token =
        _appPreferences.getUser()?.token ?? _userSessionCubit.state?.token;
    if (token == null) {
      throw ChatSocketConnectionError('Token not found');
    }
    return _webSocketService.connect(token);
  }

  /// This method is used to close the WebSocket connection
  /// It simply disconnects the WebSocket
  @override
  void closeConnection() => _webSocketService.disconnect();
}
