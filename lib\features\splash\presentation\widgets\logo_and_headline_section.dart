import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class LogoAndHeadlineSection extends StatelessWidget {
  const LogoAndHeadlineSection({super.key});

  @override
  Widget build(BuildContext context) {
    final deviceWidth = MediaQuery.sizeOf(context).width;
    return Column(
      children: [
        SizedBox(
          height: deviceWidth * 0.3,
          // width: 95,
          child: SvgPicture.asset(
            Drawables.bbLogoWithTm,
          ),
        ),
        // const SizedBox(height: 32),
        spacerH20,
        // Text(
        //   Strings.appName,
        //   style: AppStyles.largeBold4x.copyWith(
        //     color: context.colorScheme.onPrimaryFixed,
        //     fontSize: deviceWidth * 0.1,
        //   ),
        // ),
        SvgPicture.asset(
          Drawables.bbLogoText,
          width: deviceWidth * 0.7,
        ),
        Text(
          Strings.splashHeadline,
          style: context.typography.large1xSemi.copyWith(
            color: context.colorScheme.onPrimaryFixed,
            fontSize: deviceWidth * 0.058,
          ),
        ),
      ],
    );
  }
}
