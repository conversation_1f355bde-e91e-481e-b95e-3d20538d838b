import 'dart:convert';

import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/network/interceptors/auth_interceptor.dart';
import 'package:buyer_board/data/sources/local/preferences/app_preferences.dart';
import 'package:buyer_board/features/chat/data/datasources/chat_websocket_service.dart';
import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/data/models/chat_payload.dart';
import 'package:buyer_board/features/chat/domain/repositories/chat_notification_repository.dart';
import 'package:buyer_board/features/chat/presentation/bloc/chat_socket_connection_cubit.dart';

final class ChatNotificationsRepositoryImpl
    implements ChatNotificationsRepository {
  final WebSocketService _webSocketService;
  final AppPreferences _appPreferences;
  final UserSessionCubit _userSessionCubit;
  ChatNotificationsRepositoryImpl(this._webSocketService)
      : _appPreferences = Injector.resolve<AppPreferences>(),
        _userSessionCubit = Injector.resolve<UserSessionCubit>();

  /// Sends the payload to the server
  Future<void> _sendPayload(ChatPayload payload) async {
     _webSocketService.sendMessage(jsonEncode(payload.toJson()));
  }

  /// This method is used to map the event received from the server
  /// to the enum value
  // ChatEventType _mapEvent(String event) {
  //   return switch (event) {
  //     'phx_reply' => ChatEventType.phxReply,
  //     'new_message' => ChatEventType.newMessage,
  //     'edit_message' => ChatEventType.editMessage,
  //     'delete_message' => ChatEventType.deleteMessage,
  //     'archive_chat' => ChatEventType.archiveChat,
  //     'unarchive_chat' => ChatEventType.unarchiveChat,
  //     _ => ChatEventType.other,
  //   };
  // }

  /// This method is used to initialize the WebSocket connection
  /// It uses the token from the user session to establish the connection
  /// If the token is not found, it throws an error
  @override
  void initializeConnection() {
    final token =
        _appPreferences.getUser()?.token ?? _userSessionCubit.state?.token;
    if (token == null) {
      throw ChatSocketConnectionError('Token not found');
    }
    return _webSocketService.connect(token);
  }

  /// This method is used to close the WebSocket connection
  /// It simply disconnects the WebSocket
  @override
  void closeConnection() => _webSocketService.disconnect();

  /// This method is used to subscribe to the notifications
  /// It sends the payload to the server and listens to the response
  /// If a new message is received, it notifies its listeners
  @override
  Stream<ChatMessage> subscribeNotifications() async* {
    final userId = _appPreferences.getUser()?.id ?? _userSessionCubit.state?.id;
    if (userId == null) {
      throw ChatSocketConnectionError('Unauthorized');
    }
    _sendPayload(ChatNotificationsPayload(userId: userId));
    await for (final message in _webSocketService.stream) {
      final decodedResponse = jsonDecode(message);
      final event = decodedResponse['event'];
      final Map<String, dynamic>? response;

      switch (event) {
        case 'new_message':
          if (decodedResponse['payload']['message'] != null) {
            response =
                decodedResponse['payload']['message'] as Map<String, dynamic>;
          } else {
            response = null;
          }
        default:
          response = null;
      }

      if (response != null) {
        final chatMessage = ChatMessage.fromJson(response);
        yield chatMessage;
      }
    }
  }
}
