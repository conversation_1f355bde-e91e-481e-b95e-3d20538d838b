// import 'package:flutter/material.dart';

// class AppButton extends StatelessWidget {
//   const AppButton({
//     super.key,
//     required this.onPressed,
//     this.label,
//   })  : icon = null,
//         _type = _Type.normal;

//   const AppButton.icon({
//     super.key,
//     required this.onPressed,
//     required this.icon,
//   })  : label = null,
//         _type = _Type.icon;

//   const AppButton.text({
//     super.key,
//     required this.onPressed,
//     required this.label,
//   })  : icon = null,
//         _type = _Type.text;

//   const AppButton.textIcon({
//     super.key,
//     required this.onPressed,
//     required this.label,
//     required this.icon,
//   }) : _type = _Type.textIcon;

//   final VoidCallback onPressed;
//   final String? label;
//   final IconData? icon;
//   final _Type _type;

//   @override
//   Widget build(BuildContext context) {
//     return switch(_type) {
//       case _Type.normal:
//         return _buildNormalButton();
//       case _Type.text:
//         return _buildTextButton();
//       case _Type.icon:
//         return _buildIconButton();
//       case _Type.textIcon:
//         return _buildTextIconButton();
//     };
//     }
//   }
// }

// enum AppButtonDirection {
//   ltr,
//   rtl,
// }

// enum _Type {
//   normal,
//   text,
//   icon,
//   textIcon,
// }
