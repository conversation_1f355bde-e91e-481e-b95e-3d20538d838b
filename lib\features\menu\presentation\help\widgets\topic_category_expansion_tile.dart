import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/features/menu/data/models/help_response.dart';
import 'package:flutter/material.dart';

class TopicCategoryExpansionTile extends StatefulWidget {
  const TopicCategoryExpansionTile({super.key, required this.category});
  final HelpCategory category;

  @override
  State<TopicCategoryExpansionTile> createState() =>
      _TopicCategoryExpansionTileState();
}

class _TopicCategoryExpansionTileState
    extends State<TopicCategoryExpansionTile> {
  bool _isExpanded = false;
  @override
  Widget build(BuildContext context) {
    return ListTileTheme(
      contentPadding: EdgeInsets.zero,
      dense: true,
      child: ExpansionTile(
        childrenPadding: EdgeInsets.zero,
        onExpansionChanged: (value) {
          setState(() {
            _isExpanded = value;
          });
        },
        trailing: AnimatedRotation(
          turns: _isExpanded ? 0.5 : 0,
          duration: const Duration(milliseconds: 200),
          child: Icon(
            Icons.arrow_drop_down,
            size: 34,
            color: _isExpanded
                ? context.appColors.pPXLight
                : context.appColors.greyM,
          ),
        ),
        title: Text(
          widget.category.title,
          style: context.typography.mediumReg.copyWith(
            color: context.appColors.pPXLight,
          ),
        ),
      ),
    );
  }
}
