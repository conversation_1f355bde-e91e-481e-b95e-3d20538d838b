import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/theme/app_theme.dart';
import 'package:buyer_board/features/home/<USER>/bloc/buyers_cubit.dart';
import 'package:buyer_board/features/home/<USER>/screens/expanded_buyer_card.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChatBuyerExpandedCard extends StatefulWidget {
  const ChatBuyerExpandedCard({super.key, required this.id});

  final int id;

  @override
  State<ChatBuyerExpandedCard> createState() => _ChatBuyerExpandedCardState();
}

class _ChatBuyerExpandedCardState extends State<ChatBuyerExpandedCard> {
  @override
  void initState() {
    super.initState();
    context.read<BuyerCubit>().fetchBuyer(widget.id);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BuyerCubit, BuyerCubitState>(
      builder: (context, state) {
        if (state is BuyerCubitLoading) {
          return Center(
            child: Scaffold(
              appBar: AppBar(),
              body: Center(
                  child: CupertinoActivityIndicator(
                radius: 20,
                color: context.theme.appColors.pPXLight,
              )),
            ),
          );
        } else if (state is BuyerCubitLoaded) {
          return ExpandedBuyerCard(
            buyer: state.buyers,
            showChatWithMe: false,
          );
        } else if (state is BuyerCubitError) {
          return Center(
            child: Text(state.error),
          );
        }
        return const SizedBox();
      },
    );
  }
}
