// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_profile_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpdateProfileRequest _$UpdateProfileRequestFromJson(
        Map<String, dynamic> json) =>
    UpdateProfileRequest(
      email: json['agent_email'] as String?,
      avatar: json['avatar'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      primaryPhoneNumber: json['phone_number_primary'] as String?,
      brokerageName: json['brokerage_name'] as String?,
      brokerageLisenceNo: json['brokerage_lisence_no'] as String?,
      brokerageStreetAddress: json['broker_street_address'] as String?,
      brokerageCity: json['broker_city'] as String?,
      brokerageZipCode: json['brokerage_zip_code'] as String?,
      brokerageState: json['brokerage_state'] as String?,
      agentLicenseIdNo: json['lisence_id_no'] as String?,
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$UpdateProfileRequestToJson(
    UpdateProfileRequest instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('agent_email', instance.email);
  writeNotNull('avatar', instance.avatar);
  writeNotNull('first_name', instance.firstName);
  writeNotNull('last_name', instance.lastName);
  writeNotNull('phone_number_primary', instance.primaryPhoneNumber);
  writeNotNull('brokerage_name', instance.brokerageName);
  writeNotNull('brokerage_lisence_no', instance.brokerageLisenceNo);
  writeNotNull('broker_street_address', instance.brokerageStreetAddress);
  writeNotNull('broker_city', instance.brokerageCity);
  writeNotNull('brokerage_zip_code', instance.brokerageZipCode);
  writeNotNull('brokerage_state', instance.brokerageState);
  writeNotNull('lisence_id_no', instance.agentLicenseIdNo);
  writeNotNull('image_url', instance.imageUrl);
  return val;
}
