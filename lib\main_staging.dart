import 'package:buyer_board/core/app/app.dart';
import 'package:buyer_board/core/app/app_config.dart';
import 'package:buyer_board/core/di/injector.dart';
import 'package:buyer_board/core/notification_manager/notification_service.dart';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Injector.setup(appConfig: AppConfig.staging());
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: HydratedStorageDirectory(
        (await getApplicationDocumentsDirectory()).path),
  );
  await NotificationService.initialize(AppConfig.staging());

  await SentryFlutter.init(
    (options) {
      options.dsn =
          'https://<EMAIL>/3';
      options.environment = "staging";
      options.tracesSampleRate = 1.0;
    },
    appRunner: () => runApp(const BuyerBoardApp()),
  );
}
