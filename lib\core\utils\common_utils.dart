import 'dart:io';

import 'package:flutter/material.dart';

class CommonUtils {
  Future<bool> checkInternetConnectivity() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        debugPrint('connected');
        return true;
      }
    } on SocketException catch (_) {
      debugPrint('not connected');
      return false;
    }
    return false;
  }

  static Future<bool> hasNetwork() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  static bool validatePhoneNumber(String value) {
    String pattern = r'(^[0-9]{8,15}$)';
    RegExp regExp = RegExp(pattern);
    return regExp.hasMatch(value);
  }

  static String maskPhoneCharacters(String inputString) {
    const maskLength = 6;
    if (inputString.length <= maskLength) {
      /// Mask the entire string if it's shorter or equal to 6 characters
      return '*' * inputString.length;
    } else {
      String maskedPart = '*' * maskLength;
      String remainingPart = inputString.substring(maskLength);
      return maskedPart + remainingPart;
    }
  }

  static String maskCardNumberCharacters(String inputString) {
    const maskLength = 4;
    if (inputString.length <= maskLength) {
      /// Mask the entire string if it's shorter or equal to 6 characters
      return 'x' * inputString.length;
    } else {
      String maskedPart = 'x' * maskLength;
      String remainingPart = inputString.substring(inputString.length - 5);
      return maskedPart + remainingPart;
    }
  }

  static void hideKeyboard() => FocusManager.instance.primaryFocus?.unfocus();
}
