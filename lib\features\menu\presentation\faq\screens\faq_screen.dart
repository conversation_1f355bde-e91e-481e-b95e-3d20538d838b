import 'package:buyer_board/common/widgets/app_bar.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/core/resources/resources.dart';
import 'package:buyer_board/features/menu/presentation/faq/widgets/faq_questions_list.dart';
import 'package:flutter/material.dart';

class FaqScreen extends StatelessWidget {
  const FaqScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ApplicationAppBar.buildAppBar(
        context,
        title: Strings.faqs,
        leadingWidget: IconButton(
          icon: const Icon(
            Icons.close,
            size: Dimensions.padding_28,
          ),
          onPressed: () => context.shouldPop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(Dimensions.materialPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.top10Questions,
              style: context.typography.large1xBlack,
            ),
            Text(
              Strings.mostPopularQuestions,
              style: context.typography.mediumReg,
            ),
            const SizedBox(
              height: Dimensions.materialPadding,
            ),
            const FaqQuestionsList(),
          ],
        ),
      ),
    );
  }
}
