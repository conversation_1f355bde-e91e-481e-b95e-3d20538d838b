// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sign_up_with_email_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SignUpWithEmailState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message) success,
    required TResult Function(String message) rememberMe,
    required TResult Function(String? error) signupError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message)? success,
    TResult? Function(String message)? rememberMe,
    TResult? Function(String? error)? signupError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message)? success,
    TResult Function(String message)? rememberMe,
    TResult Function(String? error)? signupError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(signupError value) signupError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(signupError value)? signupError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(signupError value)? signupError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignUpWithEmailStateCopyWith<$Res> {
  factory $SignUpWithEmailStateCopyWith(SignUpWithEmailState value,
          $Res Function(SignUpWithEmailState) then) =
      _$SignUpWithEmailStateCopyWithImpl<$Res, SignUpWithEmailState>;
}

/// @nodoc
class _$SignUpWithEmailStateCopyWithImpl<$Res,
        $Val extends SignUpWithEmailState>
    implements $SignUpWithEmailStateCopyWith<$Res> {
  _$SignUpWithEmailStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$initialImplCopyWith<$Res> {
  factory _$$initialImplCopyWith(
          _$initialImpl value, $Res Function(_$initialImpl) then) =
      __$$initialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$initialImplCopyWithImpl<$Res>
    extends _$SignUpWithEmailStateCopyWithImpl<$Res, _$initialImpl>
    implements _$$initialImplCopyWith<$Res> {
  __$$initialImplCopyWithImpl(
      _$initialImpl _value, $Res Function(_$initialImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$initialImpl implements initial {
  const _$initialImpl();

  @override
  String toString() {
    return 'SignUpWithEmailState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$initialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message) success,
    required TResult Function(String message) rememberMe,
    required TResult Function(String? error) signupError,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message)? success,
    TResult? Function(String message)? rememberMe,
    TResult? Function(String? error)? signupError,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message)? success,
    TResult Function(String message)? rememberMe,
    TResult Function(String? error)? signupError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(signupError value) signupError,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(signupError value)? signupError,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(signupError value)? signupError,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class initial implements SignUpWithEmailState {
  const factory initial() = _$initialImpl;
}

/// @nodoc
abstract class _$$loadingImplCopyWith<$Res> {
  factory _$$loadingImplCopyWith(
          _$loadingImpl value, $Res Function(_$loadingImpl) then) =
      __$$loadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$loadingImplCopyWithImpl<$Res>
    extends _$SignUpWithEmailStateCopyWithImpl<$Res, _$loadingImpl>
    implements _$$loadingImplCopyWith<$Res> {
  __$$loadingImplCopyWithImpl(
      _$loadingImpl _value, $Res Function(_$loadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$loadingImpl implements loading {
  const _$loadingImpl();

  @override
  String toString() {
    return 'SignUpWithEmailState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$loadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message) success,
    required TResult Function(String message) rememberMe,
    required TResult Function(String? error) signupError,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message)? success,
    TResult? Function(String message)? rememberMe,
    TResult? Function(String? error)? signupError,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message)? success,
    TResult Function(String message)? rememberMe,
    TResult Function(String? error)? signupError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(signupError value) signupError,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(signupError value)? signupError,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(signupError value)? signupError,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class loading implements SignUpWithEmailState {
  const factory loading() = _$loadingImpl;
}

/// @nodoc
abstract class _$$successImplCopyWith<$Res> {
  factory _$$successImplCopyWith(
          _$successImpl value, $Res Function(_$successImpl) then) =
      __$$successImplCopyWithImpl<$Res>;
  @useResult
  $Res call({User authResponse, String message});
}

/// @nodoc
class __$$successImplCopyWithImpl<$Res>
    extends _$SignUpWithEmailStateCopyWithImpl<$Res, _$successImpl>
    implements _$$successImplCopyWith<$Res> {
  __$$successImplCopyWithImpl(
      _$successImpl _value, $Res Function(_$successImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authResponse = null,
    Object? message = null,
  }) {
    return _then(_$successImpl(
      null == authResponse
          ? _value.authResponse
          : authResponse // ignore: cast_nullable_to_non_nullable
              as User,
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$successImpl implements success {
  const _$successImpl(this.authResponse, this.message);

  @override
  final User authResponse;
  @override
  final String message;

  @override
  String toString() {
    return 'SignUpWithEmailState.success(authResponse: $authResponse, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$successImpl &&
            (identical(other.authResponse, authResponse) ||
                other.authResponse == authResponse) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, authResponse, message);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      __$$successImplCopyWithImpl<_$successImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message) success,
    required TResult Function(String message) rememberMe,
    required TResult Function(String? error) signupError,
  }) {
    return success(authResponse, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message)? success,
    TResult? Function(String message)? rememberMe,
    TResult? Function(String? error)? signupError,
  }) {
    return success?.call(authResponse, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message)? success,
    TResult Function(String message)? rememberMe,
    TResult Function(String? error)? signupError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(authResponse, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(signupError value) signupError,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(signupError value)? signupError,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(signupError value)? signupError,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class success implements SignUpWithEmailState {
  const factory success(final User authResponse, final String message) =
      _$successImpl;

  User get authResponse;
  String get message;

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$successImplCopyWith<_$successImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$rememberMeImplCopyWith<$Res> {
  factory _$$rememberMeImplCopyWith(
          _$rememberMeImpl value, $Res Function(_$rememberMeImpl) then) =
      __$$rememberMeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$rememberMeImplCopyWithImpl<$Res>
    extends _$SignUpWithEmailStateCopyWithImpl<$Res, _$rememberMeImpl>
    implements _$$rememberMeImplCopyWith<$Res> {
  __$$rememberMeImplCopyWithImpl(
      _$rememberMeImpl _value, $Res Function(_$rememberMeImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$rememberMeImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$rememberMeImpl implements rememberMe {
  const _$rememberMeImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'SignUpWithEmailState.rememberMe(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$rememberMeImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$rememberMeImplCopyWith<_$rememberMeImpl> get copyWith =>
      __$$rememberMeImplCopyWithImpl<_$rememberMeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message) success,
    required TResult Function(String message) rememberMe,
    required TResult Function(String? error) signupError,
  }) {
    return rememberMe(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message)? success,
    TResult? Function(String message)? rememberMe,
    TResult? Function(String? error)? signupError,
  }) {
    return rememberMe?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message)? success,
    TResult Function(String message)? rememberMe,
    TResult Function(String? error)? signupError,
    required TResult orElse(),
  }) {
    if (rememberMe != null) {
      return rememberMe(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(signupError value) signupError,
  }) {
    return rememberMe(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(signupError value)? signupError,
  }) {
    return rememberMe?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(signupError value)? signupError,
    required TResult orElse(),
  }) {
    if (rememberMe != null) {
      return rememberMe(this);
    }
    return orElse();
  }
}

abstract class rememberMe implements SignUpWithEmailState {
  const factory rememberMe(final String message) = _$rememberMeImpl;

  String get message;

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$rememberMeImplCopyWith<_$rememberMeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$signupErrorImplCopyWith<$Res> {
  factory _$$signupErrorImplCopyWith(
          _$signupErrorImpl value, $Res Function(_$signupErrorImpl) then) =
      __$$signupErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$$signupErrorImplCopyWithImpl<$Res>
    extends _$SignUpWithEmailStateCopyWithImpl<$Res, _$signupErrorImpl>
    implements _$$signupErrorImplCopyWith<$Res> {
  __$$signupErrorImplCopyWithImpl(
      _$signupErrorImpl _value, $Res Function(_$signupErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$signupErrorImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$signupErrorImpl implements signupError {
  const _$signupErrorImpl(this.error);

  @override
  final String? error;

  @override
  String toString() {
    return 'SignUpWithEmailState.signupError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$signupErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$signupErrorImplCopyWith<_$signupErrorImpl> get copyWith =>
      __$$signupErrorImplCopyWithImpl<_$signupErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(User authResponse, String message) success,
    required TResult Function(String message) rememberMe,
    required TResult Function(String? error) signupError,
  }) {
    return signupError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(User authResponse, String message)? success,
    TResult? Function(String message)? rememberMe,
    TResult? Function(String? error)? signupError,
  }) {
    return signupError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(User authResponse, String message)? success,
    TResult Function(String message)? rememberMe,
    TResult Function(String? error)? signupError,
    required TResult orElse(),
  }) {
    if (signupError != null) {
      return signupError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(initial value) initial,
    required TResult Function(loading value) loading,
    required TResult Function(success value) success,
    required TResult Function(rememberMe value) rememberMe,
    required TResult Function(signupError value) signupError,
  }) {
    return signupError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(initial value)? initial,
    TResult? Function(loading value)? loading,
    TResult? Function(success value)? success,
    TResult? Function(rememberMe value)? rememberMe,
    TResult? Function(signupError value)? signupError,
  }) {
    return signupError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(initial value)? initial,
    TResult Function(loading value)? loading,
    TResult Function(success value)? success,
    TResult Function(rememberMe value)? rememberMe,
    TResult Function(signupError value)? signupError,
    required TResult orElse(),
  }) {
    if (signupError != null) {
      return signupError(this);
    }
    return orElse();
  }
}

abstract class signupError implements SignUpWithEmailState {
  const factory signupError(final String? error) = _$signupErrorImpl;

  String? get error;

  /// Create a copy of SignUpWithEmailState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$signupErrorImplCopyWith<_$signupErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
