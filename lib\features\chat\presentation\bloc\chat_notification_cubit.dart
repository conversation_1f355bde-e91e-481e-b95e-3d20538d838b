import 'package:buyer_board/features/chat/data/models/chat_message.dart';
import 'package:buyer_board/features/chat/domain/repositories/chat_notification_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

sealed class ChatNotificationState {}

class ChatNotificationInitial extends ChatNotificationState {}

class ChatNotificationReceived extends ChatNotificationState {
  final ChatMessage message;

  ChatNotificationReceived(this.message);
}

class ChatNotificationCubit extends Cubit<ChatNotificationState> {
  final ChatNotificationsRepository repository;

  ChatNotificationCubit({
    required this.repository,
  }) : super(ChatNotificationInitial()) {
    _subscribeToMessages();
  }

  void _subscribeToMessages() {
    repository.initializeConnection();
    repository.subscribeNotifications().listen((message) {
      print("Message Received: $message");
      emit(ChatNotificationReceived(message));
    });
  }

  void closeConnection() {
    repository.closeConnection();
  }
}
