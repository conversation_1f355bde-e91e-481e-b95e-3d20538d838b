import 'package:buyer_board/common/widgets/profile_text_field.dart';
import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:buyer_board/features/add_buyer/presentation/cubit/buyer_info_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../core/resources/resources.dart';
import '../../../../core/utils/validators.dart';

class BuyerExpirationDateSection extends StatefulWidget {
  const BuyerExpirationDateSection(
      {super.key, required this.expirationDateTimeController});
  final TextEditingController expirationDateTimeController;

  @override
  State<BuyerExpirationDateSection> createState() =>
      _BuyerExpirationDateSectionState();
}

class _BuyerExpirationDateSectionState
    extends State<BuyerExpirationDateSection> {
  @override
  void initState() {
    super.initState();
    final state = context.read<BuyerInfoCubit>().state;
    context.read<BuyerInfoCubit>().setAgreementExpirationDate(
          state.buyerExpirationDate ??
              DateTime.now().add(
                const Duration(days: 90),
              ),
        );
  }

  void onSelectDateTime() async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: _getInitialDate(widget.expirationDateTimeController.text),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (date != null) {
      context.read<BuyerInfoCubit>().setAgreementExpirationDate(date);
      setState(() {
        widget.expirationDateTimeController.text =
            DateFormat(Constants.dateFormatter).format(date);
      });
    }
  }

  DateTime _getInitialDate(String dateText) {
    try {
      return DateFormat('MM/dd/yyyy').parse(dateText);
    } catch (e) {
      return DateTime.now().add(const Duration(days: 90));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.agreementExpirationDate,
          style: context.typography.large1xBlack,
        ),
        Text(
          Strings.buyerExpirationDateDesc,
          style: context.typography.mediumReg,
        ),
        const SizedBox(height: 16),
        ProfileTextField(
          onTap: onSelectDateTime,
          controller: widget.expirationDateTimeController,
          validator: Validators().requiredFieldValidator,
          label: Strings.expirationDate,
          hint: Strings.expirationDateHint,
          readOnly: true,
        ),
      ],
    );
  }
}
