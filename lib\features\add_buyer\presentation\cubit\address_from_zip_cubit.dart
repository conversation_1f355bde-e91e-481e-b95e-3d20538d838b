import 'package:buyer_board/features/profile/data/models/response/user_address_response.dart';
import 'package:buyer_board/features/profile/domain/repository/user_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddressFromZipCubit extends Cubit<void> {
  final UserRepository userRepository;
  AddressFromZipCubit({required this.userRepository}) : super(null);

  Future<UserAddress?> getAddress({required String zipCode}) async {
    try {
      final response =
          await userRepository.getAddressFromZipCode(zipCode: zipCode);
      if (response.data != null) {
        final result = response.data;
        return result;
      }
    } catch (e) {
      return null;
    }
    return null; // Handle error cases appropriately
  }
}
