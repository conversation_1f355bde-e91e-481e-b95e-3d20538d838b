import 'package:json_annotation/json_annotation.dart';

part 'signup_auth_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake, explicitToJson: true)
class SignUpAuthRequest {
  SignUpAuthRequest({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
  });

  final String email;
  final String password;
  final String firstName;
  final String lastName;

  Map<String, dynamic> toJson() => _$SignUpAuthRequestToJson(this);
}
