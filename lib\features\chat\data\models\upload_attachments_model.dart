import 'package:json_annotation/json_annotation.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class UploadAttachment {
  UploadAttachment({
    required this.type,
    required this.url,
    this.thumbnailUrl, // Make thumbnailUrl nullable
  });

  final String type;
  final String url;
  final String? thumbnailUrl; // Nullable field for the thumbnail URL

  // From JSON
  factory UploadAttachment.fromJson(Map<String, dynamic> json) =>
      _$UploadAttachmentFromJson(json);

  // To JSON
  Map<String, dynamic> toJson() => _$UploadAttachmentToJson(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadAttachment _$UploadAttachmentFromJson(Map<String, dynamic> json) =>
    UploadAttachment(
      type: json['type'] as String,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnail_url'] as String?, // Deserialize thumbnail_url only if available
    );

Map<String, dynamic> _$UploadAttachmentToJson(UploadAttachment instance) =>
    <String, dynamic>{
      'type': instance.type,
      'url': instance.url,
      if (instance.thumbnailUrl != null) 'thumbnail_url': instance.thumbnailUrl, // Only include thumbnail_url if it's not null
    };
