import 'package:buyer_board/core/extensions/build_context.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../../common/widgets/profile_text_field.dart';
import '../../../../core/resources/resources.dart';
import '../../../../core/utils/validators.dart';

class PropertyAttribute extends StatelessWidget {
  const PropertyAttribute({
    super.key,
    required this.attributeController,
    required this.label,
    required this.icon,
    this.onChanged,
    this.inputFormatters,
  });
  final TextEditingController attributeController;
  final Widget icon;
  final String label;
  final Function(String?)? onChanged;
  final List<TextInputFormatter>? inputFormatters;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: context.colorScheme.primary,
            ),
            width: double.maxFinite,
            padding: const EdgeInsets.symmetric(vertical: Dimensions.padding_8),
            child: Sized<PERSON><PERSON>(
              height: 24,
              child: icon,
            ),
          ),
          spacerH8,
          ProfileTextField(
            controller: attributeController,
            onChanged: onChanged,
            onClear: () => onChanged?.call(''),
            inputFormatters: [
              ...inputFormatters ?? [],
            ],
            keyboardType: const TextInputType.numberWithOptions(
              decimal: true,
            ),
            validator: Validators().requiredFieldValidator,
            label: label,
            hint: Strings.value,
          ),
        ],
      ),
    );
  }
}
